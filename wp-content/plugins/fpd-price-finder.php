<?php
/*
Plugin Name: FPD Design Price Display
Description: Display design prices from Fancy Product Designer database in the design library
Version: 1.0.0
Author: Custom Solution
*/

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class FPD_Price_Finder {

    public function __construct() {
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('wp_ajax_fpd_get_all_prices', array($this, 'ajax_get_all_prices'));
        add_action('wp_ajax_nopriv_fpd_get_all_prices', array($this, 'ajax_get_all_prices'));
    }


    
    public function enqueue_scripts() {
        wp_add_inline_style('wp-block-library', '
        .fpd-item { position: relative; }
        .fpd-price-tag {
            position: absolute;
            top: 5px;
            right: 5px;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            z-index: 100;
            box-shadow: 0 2px 4px rgba(0,0,0,0.3);
            font-family: Arial, sans-serif;
        }
        .fpd-price-tag.free {
            background: #27ae60;
            color: white;
        }
        .fpd-price-tag.paid {
            background: #e74c3c;
            color: white;
        }
        .fpd-price-tag.loading {
            background: #3498db;
            color: white;
            animation: blink 1s infinite;
        }
        @keyframes blink { 0%, 50% { opacity: 1; } 51%, 100% { opacity: 0.5; } }
        ');
        
        wp_enqueue_script('jquery');
        add_action('wp_footer', array($this, 'add_script'));
        
        wp_localize_script('jquery', 'fpd_finder', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('fpd_finder_nonce'),
            'currency' => function_exists('get_woocommerce_currency_symbol') ? get_woocommerce_currency_symbol() : '$'
        ));
    }
    
    public function add_script() {
        ?>
        <script>
        jQuery(document).ready(function($) {
            console.log('FPD Fast Prices: Initializing...');

            var priceCache = {};
            var pricesLoaded = false;

            // Load all prices at once - MUCH FASTER!
            function loadAllPrices() {
                if (pricesLoaded) return;

                console.log('FPD Fast Prices: Loading all prices in one request...');

                $.ajax({
                    url: fpd_finder.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'fpd_get_all_prices',
                        nonce: fpd_finder.nonce
                    },
                    success: function(response) {
                        if (response.success) {
                            priceCache = response.data.prices;
                            pricesLoaded = true;
                            console.log('FPD Fast Prices: Loaded', Object.keys(priceCache).length, 'prices instantly!');

                            // Apply prices to all visible designs immediately
                            applyPricesToDesigns();
                        }
                    },
                    error: function() {
                        console.error('FPD Fast Prices: Failed to load prices');
                        pricesLoaded = true; // Prevent retry
                    }
                });
            }

            function applyPricesToDesigns() {
                $('.fpd-item[data-source]').each(function() {
                    var $item = $(this);
                    if ($item.find('.fpd-price-tag').length > 0) return;

                    var designUrl = $item.attr('data-source');
                    if (!designUrl) return;

                    var filename = designUrl.split('/').pop();
                    var price = priceCache[filename] || 0;

                    showPrice($item, price);
                });
            }
            
            function showPrice($item, price) {
                $item.find('.fpd-price-tag').remove();

                var text, className;
                if (price > 0) {
                    text = price.toFixed(2) + ' ' + fpd_finder.currency;
                    className = 'fpd-price-tag paid';
                } else {
                    text = 'Free';
                    className = 'fpd-price-tag free';
                }

                var $tag = $('<span class="' + className + '">' + text + '</span>');
                $item.append($tag);

                console.log('FPD Finder: Displayed price', text, 'for item');
            }
            
            function processDesigns() {
                var $items = $('.fpd-item[data-source]');
                console.log('FPD Fast Prices: Found', $items.length, 'design items');

                if ($items.length > 0) {
                    if (!pricesLoaded) {
                        loadAllPrices(); // Load all prices in one go
                    } else {
                        applyPricesToDesigns(); // Apply cached prices instantly
                    }
                }
            }

            // Initial run
            setTimeout(processDesigns, 500);

            // Watch for changes
            var observer = new MutationObserver(function() {
                setTimeout(processDesigns, 200);
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true
            });

            // Periodic check (less frequent since we're faster)
            setInterval(processDesigns, 2000);
        });
        </script>
        <?php
    }
    
    public function ajax_get_all_prices() {
        check_ajax_referer('fpd_finder_nonce', 'nonce');

        $all_prices = $this->get_all_design_prices_fast();

        wp_send_json_success(array(
            'prices' => $all_prices,
            'count' => count($all_prices)
        ));
    }
    
    private function get_all_design_prices_fast() {
        global $wpdb;

        $designs_table = $wpdb->prefix . 'fpd_designs';
        $price_map = array();

        // Get ALL design records in one query - MUCH FASTER!
        $all_designs = $wpdb->get_results("SELECT designs FROM $designs_table WHERE designs IS NOT NULL AND designs != ''");

        foreach ($all_designs as $design_record) {
            if (!empty($design_record->designs)) {
                $designs_data = json_decode($design_record->designs, true);

                if (is_array($designs_data)) {
                    foreach ($designs_data as $design) {
                        // Extract filename from source URL
                        if (isset($design['source'])) {
                            $filename = basename($design['source']);

                            // Look for price in parameters
                            if (isset($design['parameters']['price']) && is_numeric($design['parameters']['price'])) {
                                $price = floatval($design['parameters']['price']);
                                $price_map[$filename] = $price;
                            }
                        }
                    }
                }
            }
        }

        return $price_map;
    }
}

new FPD_Price_Finder();
