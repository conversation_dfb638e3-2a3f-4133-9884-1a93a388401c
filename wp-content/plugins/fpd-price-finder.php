<?php
/*
Plugin Name: FPD Price Finder
Description: Find and display design prices correctly from FPD database
Version: 1.0.0
Author: Working Solution
*/

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class FPD_Price_Finder {
    
    public function __construct() {
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('wp_ajax_fpd_find_price', array($this, 'ajax_find_price'));
        add_action('wp_ajax_nopriv_fpd_find_price', array($this, 'ajax_find_price'));
        
        // Add admin page for debugging
        add_action('admin_menu', array($this, 'add_debug_page'));
    }
    
    public function add_debug_page() {
        add_submenu_page(
            'tools.php',
            'FPD Price Finder Debug',
            'FPD Price Debug',
            'manage_options',
            'fpd-price-finder-debug',
            array($this, 'debug_page')
        );
    }
    
    public function debug_page() {
        echo '<div class="wrap"><h1>FPD Price Finder Debug</h1>';
        
        global $wpdb;
        $designs_table = $wpdb->prefix . 'fpd_designs';
        
        // Search for baby-silver specifically
        $designs = $wpdb->get_results(
            "SELECT ID, title, options, designs FROM $designs_table WHERE designs LIKE '%baby-silver%'"
        );
        
        echo '<h2>Searching for baby-silver.webp</h2>';
        echo '<p>Found ' . count($designs) . ' records containing "baby-silver"</p>';
        
        foreach ($designs as $design) {
            echo '<div style="border: 1px solid #ccc; padding: 10px; margin: 10px 0;">';
            echo '<h3>Record ID: ' . $design->ID . ' - ' . esc_html($design->title) . '</h3>';
            
            // Parse and display designs data
            if (!empty($design->designs)) {
                $designs_data = json_decode($design->designs, true);
                echo '<h4>Designs Data:</h4>';
                
                if (is_array($designs_data)) {
                    foreach ($designs_data as $index => $individual_design) {
                        echo '<div style="margin-left: 20px; border-left: 2px solid #ddd; padding-left: 10px; margin-bottom: 10px;">';
                        echo '<h5>Design #' . ($index + 1) . '</h5>';
                        
                        if (isset($individual_design['source'])) {
                            echo '<p><strong>Source:</strong> ' . esc_html($individual_design['source']) . '</p>';
                        }
                        
                        if (isset($individual_design['title'])) {
                            echo '<p><strong>Title:</strong> ' . esc_html($individual_design['title']) . '</p>';
                        }
                        
                        echo '<p><strong>All Parameters:</strong></p>';
                        echo '<pre style="background: #f5f5f5; padding: 10px; font-size: 12px;">';
                        print_r($individual_design['parameters'] ?? 'No parameters');
                        echo '</pre>';
                        
                        // Check specifically for price
                        if (isset($individual_design['parameters'])) {
                            $params = $individual_design['parameters'];
                            if (isset($params['designs_parameter_price'])) {
                                echo '<p style="color: green;"><strong>✅ PRICE FOUND: ' . $params['designs_parameter_price'] . '</strong></p>';
                            } else {
                                echo '<p style="color: red;">❌ No designs_parameter_price found</p>';
                                
                                // Check for other price-related keys
                                foreach ($params as $key => $value) {
                                    if (strpos(strtolower($key), 'price') !== false) {
                                        echo '<p style="color: orange;">🔍 Found price-related key: ' . $key . ' = ' . $value . '</p>';
                                    }
                                }
                            }
                        }
                        
                        echo '</div>';
                    }
                } else {
                    echo '<p>Invalid JSON data</p>';
                }
            }
            
            // Check category options
            if (!empty($design->options)) {
                echo '<h4>Category Options:</h4>';
                $options = json_decode($design->options, true);
                echo '<pre style="background: #f0f0f0; padding: 10px; font-size: 12px;">';
                print_r($options);
                echo '</pre>';
            }
            
            echo '</div>';
        }
        
        echo '</div>';
    }
    
    public function enqueue_scripts() {
        wp_add_inline_style('wp-block-library', '
        .fpd-item { position: relative; }
        .fpd-price-tag {
            position: absolute;
            top: 5px;
            right: 5px;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            z-index: 100;
            box-shadow: 0 2px 4px rgba(0,0,0,0.3);
            font-family: Arial, sans-serif;
        }
        .fpd-price-tag.free {
            background: #27ae60;
            color: white;
        }
        .fpd-price-tag.paid {
            background: #e74c3c;
            color: white;
        }
        .fpd-price-tag.loading {
            background: #3498db;
            color: white;
            animation: blink 1s infinite;
        }
        @keyframes blink { 0%, 50% { opacity: 1; } 51%, 100% { opacity: 0.5; } }
        ');
        
        wp_enqueue_script('jquery');
        add_action('wp_footer', array($this, 'add_script'));
        
        wp_localize_script('jquery', 'fpd_finder', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('fpd_finder_nonce'),
            'currency' => function_exists('get_woocommerce_currency_symbol') ? get_woocommerce_currency_symbol() : '$'
        ));
    }
    
    public function add_script() {
        ?>
        <script>
        jQuery(document).ready(function($) {
            console.log('FPD Price Finder: Initializing...');
            
            var cache = {};
            
            function findAndDisplayPrice($item) {
                if ($item.find('.fpd-price-tag').length > 0) return;
                
                var designUrl = $item.attr('data-source');
                if (!designUrl) return;
                
                var filename = designUrl.split('/').pop();
                console.log('FPD Finder: Processing', filename);
                
                if (cache[filename] !== undefined) {
                    showPrice($item, cache[filename]);
                    return;
                }
                
                // Show loading
                $item.append('<span class="fpd-price-tag loading">...</span>');
                
                $.ajax({
                    url: fpd_finder.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'fpd_find_price',
                        filename: filename,
                        design_url: designUrl,
                        nonce: fpd_finder.nonce
                    },
                    success: function(response) {
                        console.log('FPD Finder: Response for', filename, ':', response);
                        
                        if (response.success) {
                            var price = parseFloat(response.data.price);
                            cache[filename] = price;
                            showPrice($item, price);
                        } else {
                            cache[filename] = 0;
                            showPrice($item, 0);
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('FPD Finder: Error for', filename, ':', error);
                        cache[filename] = 0;
                        showPrice($item, 0);
                    }
                });
            }
            
            function showPrice($item, price) {
                $item.find('.fpd-price-tag').remove();
                
                var text, className;
                if (price > 0) {
                    text = fpd_finder.currency + price.toFixed(2);
                    className = 'fpd-price-tag paid';
                } else {
                    text = 'Free';
                    className = 'fpd-price-tag free';
                }
                
                var $tag = $('<span class="' + className + '">' + text + '</span>');
                $item.append($tag);
                
                console.log('FPD Finder: Displayed price', text, 'for item');
            }
            
            function processDesigns() {
                var $items = $('.fpd-item[data-source]');
                console.log('FPD Finder: Found', $items.length, 'design items');
                
                $items.each(function() {
                    findAndDisplayPrice($(this));
                });
            }
            
            // Initial run
            setTimeout(processDesigns, 1000);
            
            // Watch for changes
            var observer = new MutationObserver(function() {
                setTimeout(processDesigns, 500);
            });
            
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
            
            // Periodic check
            setInterval(processDesigns, 3000);
        });
        </script>
        <?php
    }
    
    public function ajax_find_price() {
        check_ajax_referer('fpd_finder_nonce', 'nonce');
        
        $filename = sanitize_text_field($_POST['filename']);
        $design_url = sanitize_url($_POST['design_url']);
        
        $price = $this->find_price_in_database($filename);
        
        wp_send_json_success(array(
            'price' => $price,
            'filename' => $filename,
            'debug_info' => $this->get_debug_info($filename)
        ));
    }
    
    private function find_price_in_database($filename) {
        global $wpdb;
        
        $designs_table = $wpdb->prefix . 'fpd_designs';
        
        // Get all records that might contain this design
        $designs = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT ID, title, options, designs FROM $designs_table WHERE designs LIKE %s",
                '%' . $wpdb->esc_like($filename) . '%'
            )
        );
        
        foreach ($designs as $design_record) {
            if (!empty($design_record->designs)) {
                $designs_data = json_decode($design_record->designs, true);
                
                if (is_array($designs_data)) {
                    foreach ($designs_data as $design) {
                        // Check if this design matches our filename
                        if (isset($design['source']) && strpos($design['source'], $filename) !== false) {
                            
                            // Look for price in parameters
                            if (isset($design['parameters'])) {
                                $params = $design['parameters'];
                                
                                // Check for 'price' key first (this is where FPD stores individual design prices)
                                if (isset($params['price']) && is_numeric($params['price'])) {
                                    $price = floatval($params['price']);
                                    error_log("FPD Price Finder: Found price $price for $filename");
                                    if ($price > 0) {
                                        return $price;
                                    }
                                }

                                // Try other possible price keys as fallback
                                $price_keys = [
                                    'designs_parameter_price',
                                    'element_price',
                                    'custom_price'
                                ];

                                foreach ($price_keys as $key) {
                                    if (isset($params[$key]) && is_numeric($params[$key])) {
                                        $price = floatval($params[$key]);
                                        if ($price > 0) {
                                            return $price;
                                        }
                                    }
                                }
                                
                                // Also check for any key containing 'price'
                                foreach ($params as $key => $value) {
                                    if (strpos(strtolower($key), 'price') !== false && is_numeric($value)) {
                                        $price = floatval($value);
                                        if ($price > 0) {
                                            return $price;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        
        return 0; // No price found
    }
    
    private function get_debug_info($filename) {
        global $wpdb;
        
        $designs_table = $wpdb->prefix . 'fpd_designs';
        $designs = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT designs FROM $designs_table WHERE designs LIKE %s LIMIT 1",
                '%' . $wpdb->esc_like($filename) . '%'
            )
        );
        
        if (!empty($designs)) {
            $designs_data = json_decode($designs[0]->designs, true);
            return $designs_data;
        }
        
        return null;
    }
}

new FPD_Price_Finder();
