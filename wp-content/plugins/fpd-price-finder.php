<?php
/*
Plugin Name: FPD Design Price Display
Description: Display design prices from Fancy Product Designer database in the design library
Version: 1.0.0
Author: Custom Solution
*/

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class FPD_Price_Finder {

    public function __construct() {
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('wp_ajax_fpd_find_price', array($this, 'ajax_find_price'));
        add_action('wp_ajax_nopriv_fpd_find_price', array($this, 'ajax_find_price'));
    }


    
    public function enqueue_scripts() {
        wp_add_inline_style('wp-block-library', '
        .fpd-item { position: relative; }
        .fpd-price-tag {
            position: absolute;
            top: 5px;
            right: 5px;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            z-index: 100;
            box-shadow: 0 2px 4px rgba(0,0,0,0.3);
            font-family: Arial, sans-serif;
        }
        .fpd-price-tag.free {
            background: #27ae60;
            color: white;
        }
        .fpd-price-tag.paid {
            background: #e74c3c;
            color: white;
        }
        .fpd-price-tag.loading {
            background: #3498db;
            color: white;
            animation: blink 1s infinite;
        }
        @keyframes blink { 0%, 50% { opacity: 1; } 51%, 100% { opacity: 0.5; } }
        ');
        
        wp_enqueue_script('jquery');
        add_action('wp_footer', array($this, 'add_script'));
        
        wp_localize_script('jquery', 'fpd_finder', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('fpd_finder_nonce'),
            'currency' => function_exists('get_woocommerce_currency_symbol') ? get_woocommerce_currency_symbol() : '$'
        ));
    }
    
    public function add_script() {
        ?>
        <script>
        jQuery(document).ready(function($) {
            console.log('FPD Price Finder: Initializing...');
            
            var cache = {};
            
            function findAndDisplayPrice($item) {
                if ($item.find('.fpd-price-tag').length > 0) return;
                
                var designUrl = $item.attr('data-source');
                if (!designUrl) return;
                
                var filename = designUrl.split('/').pop();
                console.log('FPD Finder: Processing', filename);
                
                if (cache[filename] !== undefined) {
                    showPrice($item, cache[filename]);
                    return;
                }
                
                // Show loading
                $item.append('<span class="fpd-price-tag loading">...</span>');
                
                $.ajax({
                    url: fpd_finder.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'fpd_find_price',
                        filename: filename,
                        design_url: designUrl,
                        nonce: fpd_finder.nonce
                    },
                    success: function(response) {
                        console.log('FPD Finder: Response for', filename, ':', response);
                        
                        if (response.success) {
                            var price = parseFloat(response.data.price);
                            cache[filename] = price;
                            showPrice($item, price);
                        } else {
                            cache[filename] = 0;
                            showPrice($item, 0);
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('FPD Finder: Error for', filename, ':', error);
                        cache[filename] = 0;
                        showPrice($item, 0);
                    }
                });
            }
            
            function showPrice($item, price) {
                $item.find('.fpd-price-tag').remove();
                
                var text, className;
                if (price > 0) {
                    text = fpd_finder.currency + price.toFixed(2);
                    className = 'fpd-price-tag paid';
                } else {
                    text = 'Free';
                    className = 'fpd-price-tag free';
                }
                
                var $tag = $('<span class="' + className + '">' + text + '</span>');
                $item.append($tag);
                
                console.log('FPD Finder: Displayed price', text, 'for item');
            }
            
            function processDesigns() {
                var $items = $('.fpd-item[data-source]');
                console.log('FPD Finder: Found', $items.length, 'design items');
                
                $items.each(function() {
                    findAndDisplayPrice($(this));
                });
            }
            
            // Initial run
            setTimeout(processDesigns, 1000);
            
            // Watch for changes
            var observer = new MutationObserver(function() {
                setTimeout(processDesigns, 500);
            });
            
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
            
            // Periodic check
            setInterval(processDesigns, 3000);
        });
        </script>
        <?php
    }
    
    public function ajax_find_price() {
        check_ajax_referer('fpd_finder_nonce', 'nonce');
        
        $filename = sanitize_text_field($_POST['filename']);

        $price = $this->find_price_in_database($filename);

        wp_send_json_success(array(
            'price' => $price,
            'filename' => $filename
        ));
    }
    
    private function find_price_in_database($filename) {
        global $wpdb;

        $designs_table = $wpdb->prefix . 'fpd_designs';

        // Get all records that might contain this design
        $designs = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT ID, title, options, designs FROM $designs_table WHERE designs LIKE %s",
                '%' . $wpdb->esc_like($filename) . '%'
            )
        );
        
        foreach ($designs as $design_record) {
            if (!empty($design_record->designs)) {
                $designs_data = json_decode($design_record->designs, true);
                
                if (is_array($designs_data)) {
                    foreach ($designs_data as $design) {
                        // Check if this design matches our filename OR title
                        $source_match = isset($design['source']) && strpos($design['source'], $filename) !== false;
                        $title_match = isset($design['title']) && strpos($filename, $design['title']) !== false;

                        if ($source_match || $title_match) {

                            // Look for price in parameters
                            if (isset($design['parameters'])) {
                                $params = $design['parameters'];
                                
                                // Check for 'price' key first (this is where FPD stores individual design prices)
                                if (isset($params['price']) && is_numeric($params['price'])) {
                                    $price = floatval($params['price']);
                                    if ($price > 0) {
                                        return $price;
                                    }
                                }

                                // Try other possible price keys as fallback
                                $price_keys = [
                                    'designs_parameter_price',
                                    'element_price',
                                    'custom_price'
                                ];

                                foreach ($price_keys as $key) {
                                    if (isset($params[$key]) && is_numeric($params[$key])) {
                                        $price = floatval($params[$key]);
                                        if ($price > 0) {
                                            return $price;
                                        }
                                    }
                                }
                                
                                // Also check for any key containing 'price'
                                foreach ($params as $key => $value) {
                                    if (strpos(strtolower($key), 'price') !== false && is_numeric($value)) {
                                        $price = floatval($value);
                                        if ($price > 0) {
                                            return $price;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        
        return 0; // No price found
    }
}

new FPD_Price_Finder();
