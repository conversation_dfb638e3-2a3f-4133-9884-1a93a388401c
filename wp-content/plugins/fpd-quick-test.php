<?php
/*
Plugin Name: FPD Quick Test
Description: Quick test to see what's happening with design prices
Version: 1.0.0
Author: Debug
*/

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Add a simple test to the footer
add_action('wp_footer', function() {
    ?>
    <script>
    jQuery(document).ready(function($) {
        console.log('FPD Quick Test: Starting...');
        
        // Check if we have design items
        var $designItems = $('.fpd-item[data-source]');
        console.log('FPD Quick Test: Found', $designItems.length, 'design items');
        
        if ($designItems.length > 0) {
            $designItems.each(function(index) {
                var $item = $(this);
                var designUrl = $item.attr('data-source');
                var designTitle = $item.attr('data-title') || 'No title';
                
                console.log('Design', index + 1, ':', {
                    url: designUrl,
                    title: designTitle,
                    element: $item[0]
                });
                
                // Add a simple test price badge
                if (!$item.find('.fpd-test-price').length) {
                    $item.append('<span class="fpd-test-price" style="position: absolute; top: 5px; right: 5px; background: blue; color: white; padding: 2px 4px; font-size: 10px; z-index: 999;">TEST</span>');
                }
            });
            
            // Test AJAX call
            console.log('FPD Quick Test: Testing AJAX...');
            var firstDesignUrl = $designItems.first().attr('data-source');
            
            if (firstDesignUrl) {
                $.ajax({
                    url: '<?php echo admin_url('admin-ajax.php'); ?>',
                    type: 'POST',
                    data: {
                        action: 'fpd_quick_test_price',
                        design_url: firstDesignUrl,
                        nonce: '<?php echo wp_create_nonce('fpd_quick_test'); ?>'
                    },
                    success: function(response) {
                        console.log('FPD Quick Test AJAX Success:', response);
                    },
                    error: function(xhr, status, error) {
                        console.log('FPD Quick Test AJAX Error:', error);
                    }
                });
            }
        } else {
            console.log('FPD Quick Test: No design items found. Looking for other selectors...');
            
            // Check for other possible selectors
            var selectors = ['.fpd-item', '[data-source]', '.fpd-grid .fpd-item', '.fpd-designs-grid .fpd-item'];
            
            selectors.forEach(function(selector) {
                var $items = $(selector);
                if ($items.length > 0) {
                    console.log('Found', $items.length, 'items with selector:', selector);
                    $items.each(function(i) {
                        if (i < 3) { // Log first 3 items
                            console.log('Item', i, ':', {
                                selector: selector,
                                attributes: this.attributes,
                                element: this
                            });
                        }
                    });
                }
            });
        }
    });
    </script>
    <?php
});

// Add AJAX handler for quick test
add_action('wp_ajax_fpd_quick_test_price', 'fpd_quick_test_price_handler');
add_action('wp_ajax_nopriv_fpd_quick_test_price', 'fpd_quick_test_price_handler');

function fpd_quick_test_price_handler() {
    check_ajax_referer('fpd_quick_test', 'nonce');
    
    global $wpdb;
    
    $design_url = sanitize_url($_POST['design_url']);
    $filename = basename($design_url);
    
    // Check database
    $designs_table = $wpdb->prefix . 'fpd_designs';
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$designs_table'") == $designs_table;
    
    $result = array(
        'design_url' => $design_url,
        'filename' => $filename,
        'table_exists' => $table_exists,
        'table_name' => $designs_table
    );
    
    if ($table_exists) {
        // Get all designs
        $all_designs = $wpdb->get_results("SELECT ID, title, options, designs FROM $designs_table LIMIT 5");
        $result['total_designs'] = $wpdb->get_var("SELECT COUNT(*) FROM $designs_table");
        $result['sample_designs'] = array();
        
        foreach ($all_designs as $design) {
            $design_info = array(
                'id' => $design->ID,
                'title' => $design->title,
                'has_options' => !empty($design->options),
                'has_designs' => !empty($design->designs)
            );
            
            // Check for prices
            if (!empty($design->options)) {
                $options = json_decode($design->options, true);
                if (isset($options['designs_parameter_price'])) {
                    $design_info['category_price'] = $options['designs_parameter_price'];
                }
            }
            
            if (!empty($design->designs)) {
                $designs_data = json_decode($design->designs, true);
                if (is_array($designs_data)) {
                    $design_info['individual_designs_count'] = count($designs_data);
                    $design_info['individual_prices'] = array();
                    
                    foreach ($designs_data as $individual_design) {
                        if (isset($individual_design['parameters']['designs_parameter_price'])) {
                            $design_info['individual_prices'][] = array(
                                'source' => $individual_design['source'] ?? 'unknown',
                                'price' => $individual_design['parameters']['designs_parameter_price']
                            );
                        }
                    }
                }
            }
            
            $result['sample_designs'][] = $design_info;
        }
        
        // Look for specific design
        $matching_designs = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT options, designs FROM $designs_table WHERE designs LIKE %s",
                '%' . $wpdb->esc_like($filename) . '%'
            )
        );
        
        $result['matching_designs_count'] = count($matching_designs);
        $result['matching_designs'] = array();
        
        foreach ($matching_designs as $design) {
            $designs_data = json_decode($design->designs, true);
            if (is_array($designs_data)) {
                foreach ($designs_data as $individual_design) {
                    if (isset($individual_design['source']) && strpos($individual_design['source'], $filename) !== false) {
                        $result['matching_designs'][] = array(
                            'source' => $individual_design['source'],
                            'price' => $individual_design['parameters']['designs_parameter_price'] ?? 'not_set',
                            'all_parameters' => $individual_design['parameters'] ?? array()
                        );
                    }
                }
            }
        }
    }
    
    wp_send_json_success($result);
}
