<?php
/*
Plugin Name: FPD Design Price Display
Description: Adds price display to Fancy Product Designer design library items
Version: 1.0.0
Author: Custom Enhancement
*/

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class FPD_Design_Price_Display {
    
    public function __construct() {
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('wp_ajax_fpd_get_design_price', array($this, 'ajax_get_design_price'));
        add_action('wp_ajax_nopriv_fpd_get_design_price', array($this, 'ajax_get_design_price'));
    }
    
    public function enqueue_scripts() {
        // Only load on pages with FPD
        if ($this->should_load()) {
            wp_enqueue_script(
                'fpd-design-price-display',
                plugin_dir_url(__FILE__) . 'fpd-design-price-display.js',
                array('jquery'),
                '1.0.0',
                true
            );
            
            wp_localize_script('fpd-design-price-display', 'fpd_price_display', array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('fpd_design_price_nonce'),
                'currency_symbol' => get_woocommerce_currency_symbol(),
                'free_text' => __('Free', 'radykal'),
                'price_format' => get_option('woocommerce_price_format', '%1$s%2$s')
            ));
        }
    }
    
    public function ajax_get_design_price() {
        check_ajax_referer('fpd_design_price_nonce', 'nonce');
        
        $design_url = sanitize_url($_POST['design_url']);
        $design_title = sanitize_text_field($_POST['design_title']);
        
        // Try to get price from design metadata or settings
        $price = $this->get_design_price($design_url, $design_title);
        
        wp_send_json_success(array(
            'price' => $price,
            'formatted_price' => $this->format_price($price)
        ));
    }
    
    private function get_design_price($design_url, $design_title) {
        // Method 1: Check if there's a price in the design settings
        $price = 0;
        
        // Try to get price from FPD design settings
        if (function_exists('fpd_get_option')) {
            // Check if there's a specific price for this design
            $design_prices = fpd_get_option('fpd_design_prices', array());
            
            if (is_array($design_prices)) {
                // Look for price by URL or title
                foreach ($design_prices as $design_price) {
                    if (isset($design_price['url']) && $design_price['url'] === $design_url) {
                        $price = floatval($design_price['price']);
                        break;
                    }
                    if (isset($design_price['title']) && $design_price['title'] === $design_title) {
                        $price = floatval($design_price['price']);
                        break;
                    }
                }
            }
        }
        
        // Method 2: Check default design price setting
        if ($price == 0 && function_exists('fpd_get_option')) {
            $default_design_price = fpd_get_option('fpd_designs_parameter_price', 0);
            $price = floatval($default_design_price);
        }
        
        // Method 3: Try to extract price from design data attributes
        if ($price == 0) {
            // This would be implemented if designs have price data attributes
            // For now, we'll use a default or check filename patterns
            
            // Example: Check if filename contains price info
            $filename = basename($design_url);
            if (preg_match('/price[_-]?(\d+(?:\.\d{2})?)/', $filename, $matches)) {
                $price = floatval($matches[1]);
            }
        }
        
        return $price;
    }
    
    private function format_price($price) {
        if ($price <= 0) {
            return __('Free', 'radykal');
        }
        
        if (function_exists('wc_price')) {
            return strip_tags(wc_price($price));
        }
        
        $currency_symbol = get_woocommerce_currency_symbol();
        return $currency_symbol . number_format($price, 2);
    }

    private function should_load() {
        // Only load on pages with FPD
        global $post;

        // Check if we're on a product page with FPD enabled
        if (is_product() && $post && $post->ID) {
            if (function_exists('is_fancy_product') && is_fancy_product($post->ID)) {
                return true;
            }
            if (get_post_meta($post->ID, '_fpd_enabled', true) === 'yes') {
                return true;
            }
        }

        // Check if we're on a page with FPD shortcode
        if (is_page() && $post && $post->post_content) {
            if (has_shortcode($post->post_content, 'fpd')) {
                return true;
            }
        }

        // Check if FPD container exists on the page (fallback)
        if (is_admin()) {
            return false;
        }

        return false;
    }
}

// Initialize the plugin
new FPD_Design_Price_Display();
