<?php
/*
Plugin Name: FPD Simple Price Display
Description: Simple CSS-based price display for FPD design items using data attributes
Version: 1.0.0
Author: Custom Enhancement
*/

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class FPD_Simple_Price_Display {
    
    public function __construct() {
        add_action('wp_head', array($this, 'add_price_display_css'));
        add_action('wp_footer', array($this, 'add_price_display_js'));
    }
    
    public function add_price_display_css() {
        if (!$this->should_load()) {
            return;
        }
        ?>
        <style type="text/css">
        /* FPD Design Item Price Display */
        .fpd-item[data-price]:not([data-price=""]):not([data-price="0"]):after {
            content: attr(data-price);
            position: absolute;
            top: 5px;
            right: 5px;
            background: rgba(231, 76, 60, 0.9);
            color: #fff;
            padding: 3px 6px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: bold;
            z-index: 10;
            line-height: 1;
            min-width: 20px;
            text-align: center;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
        }
        
        .fpd-item[data-price="0"]:after,
        .fpd-item[data-price="free"]:after,
        .fpd-item[data-price="Free"]:after {
            content: "Free";
            background: rgba(46, 204, 113, 0.9);
        }
        
        /* Alternative: Use data-price-display attribute for formatted prices */
        .fpd-item[data-price-display]:not([data-price-display=""]):after {
            content: attr(data-price-display);
        }
        
        /* Hide price on hover to see design better */
        .fpd-item:hover:after {
            opacity: 0.7;
            transition: opacity 0.2s ease;
        }
        </style>
        <?php
    }
    
    public function add_price_display_js() {
        if (!$this->should_load()) {
            return;
        }
        ?>
        <script type="text/javascript">
        jQuery(document).ready(function($) {
            
            // Function to add price attributes to design items
            function addPriceAttributes() {
                $('.fpd-item[data-source]').each(function() {
                    var $item = $(this);
                    
                    // Skip if already has price attribute
                    if ($item.attr('data-price') !== undefined) {
                        return;
                    }
                    
                    var designUrl = $item.attr('data-source');
                    var designTitle = $item.attr('data-title') || '';
                    var price = null;
                    
                    if (designUrl) {
                        var filename = designUrl.split('/').pop().toLowerCase();
                        
                        // Check for price patterns in filename
                        var priceMatch = filename.match(/price[_-]?(\d+(?:\.\d{2})?)/);
                        if (priceMatch) {
                            price = '$' + parseFloat(priceMatch[1]).toFixed(2);
                        } else if (filename.match(/\$(\d+(?:\.\d{2})?)/)) {
                            var dollarMatch = filename.match(/\$(\d+(?:\.\d{2})?)/);
                            price = '$' + parseFloat(dollarMatch[1]).toFixed(2);
                        } else if (filename.includes('free') || filename.includes('gratis')) {
                            price = 'free';
                        } else if (filename.includes('premium') || filename.includes('paid')) {
                            price = '$5.00'; // Default premium price
                        }
                        
                        // Set the price attribute
                        if (price !== null) {
                            $item.attr('data-price-display', price);
                        } else {
                            // Default: show as free if no price found
                            $item.attr('data-price-display', 'Free');
                        }
                    }
                });
            }
            
            // Initial run
            addPriceAttributes();
            
            // Run when new content is loaded
            var observer = new MutationObserver(function(mutations) {
                var shouldRun = false;
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                        for (var i = 0; i < mutation.addedNodes.length; i++) {
                            var node = mutation.addedNodes[i];
                            if (node.nodeType === 1) {
                                var $node = $(node);
                                if ($node.is('.fpd-item') || $node.find('.fpd-item').length > 0) {
                                    shouldRun = true;
                                    break;
                                }
                            }
                        }
                    }
                });
                
                if (shouldRun) {
                    setTimeout(addPriceAttributes, 100);
                }
            });
            
            // Start observing
            var targetNode = document.querySelector('.fpd-container') || document.body;
            observer.observe(targetNode, {
                childList: true,
                subtree: true
            });
            
            // Periodic check (fallback)
            setInterval(addPriceAttributes, 2000);
            
            // Listen for FPD events
            if (typeof fancyProductDesigner !== 'undefined') {
                fancyProductDesigner.addEventListener('ready', function() {
                    setTimeout(addPriceAttributes, 500);
                });
                
                fancyProductDesigner.addEventListener('moduleSet', function() {
                    setTimeout(addPriceAttributes, 300);
                });
            }
        });
        </script>
        <?php
    }
    
    private function should_load() {
        // Only load on pages with FPD
        global $post;

        // Check if we're on a product page with FPD enabled
        if (is_product() && $post && $post->ID) {
            if (function_exists('is_fancy_product') && is_fancy_product($post->ID)) {
                return true;
            }
            if (get_post_meta($post->ID, '_fpd_enabled', true) === 'yes') {
                return true;
            }
        }

        // Check if we're on a page with FPD shortcode
        if (is_page() && $post && $post->post_content) {
            if (has_shortcode($post->post_content, 'fpd')) {
                return true;
            }
        }

        // Check if FPD container exists on the page (fallback)
        if (is_admin()) {
            return false;
        }

        return false;
    }
}

// Initialize the plugin
new FPD_Simple_Price_Display();
