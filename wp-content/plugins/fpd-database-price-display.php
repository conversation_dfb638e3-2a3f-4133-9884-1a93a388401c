<?php
/*
Plugin Name: FPD Database Price Display
Description: Displays design prices from FPD database records (set in admin interface)
Version: 1.0.0
Author: Custom Enhancement
*/

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class FPD_Database_Price_Display {
    
    public function __construct() {
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('wp_ajax_fpd_get_design_price_from_db', array($this, 'ajax_get_design_price'));
        add_action('wp_ajax_nopriv_fpd_get_design_price_from_db', array($this, 'ajax_get_design_price'));
    }
    
    public function enqueue_scripts() {
        // Load CSS for price display
        wp_add_inline_style('wp-block-library', $this->get_price_css());
        
        // Load JavaScript for price functionality
        wp_enqueue_script(
            'fpd-db-price-display',
            plugin_dir_url(__FILE__) . 'fpd-db-price-display.js',
            array('jquery'),
            '1.0.0',
            true
        );
        
        wp_localize_script('fpd-db-price-display', 'fpd_db_price_display', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('fpd_db_price_nonce'),
            'currency_symbol' => function_exists('get_woocommerce_currency_symbol') ? get_woocommerce_currency_symbol() : '$',
            'free_text' => __('Free', 'radykal'),
            'loading_text' => __('...', 'radykal')
        ));
    }
    
    private function get_price_css() {
        return '
        /* FPD Database Price Display */
        .fpd-item {
            position: relative;
        }
        
        .fpd-item .fpd-db-price {
            position: absolute;
            top: 5px;
            right: 5px;
            background: rgba(0, 0, 0, 0.8);
            color: #fff;
            padding: 3px 6px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: bold;
            z-index: 10;
            line-height: 1;
            min-width: 20px;
            text-align: center;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
            font-family: Arial, sans-serif;
        }
        
        .fpd-item .fpd-db-price.fpd-free {
            background: rgba(46, 204, 113, 0.9);
        }
        
        .fpd-item .fpd-db-price.fpd-premium {
            background: rgba(231, 76, 60, 0.9);
        }
        
        .fpd-item .fpd-db-price.fpd-loading {
            background: rgba(52, 152, 219, 0.9);
            animation: fpd-pulse 1.5s ease-in-out infinite alternate;
        }
        
        @keyframes fpd-pulse {
            from { opacity: 0.6; }
            to { opacity: 1; }
        }
        
        .fpd-item:hover .fpd-db-price {
            opacity: 0.7;
            transition: opacity 0.2s ease;
        }
        ';
    }
    
    public function ajax_get_design_price() {
        check_ajax_referer('fpd_db_price_nonce', 'nonce');
        
        $design_url = sanitize_url($_POST['design_url']);
        
        if (empty($design_url)) {
            wp_send_json_error('Invalid design URL');
        }
        
        $price = $this->get_design_price_from_database($design_url);
        
        wp_send_json_success(array(
            'price' => $price,
            'formatted_price' => $this->format_price($price),
            'design_url' => $design_url
        ));
    }
    
    private function get_design_price_from_database($design_url) {
        global $wpdb;
        
        // Get the filename from URL for matching
        $filename = basename($design_url);
        $filename_no_ext = pathinfo($filename, PATHINFO_FILENAME);
        
        // Define the designs table name
        $designs_table = $wpdb->prefix . 'fpd_designs';
        
        // Check if the designs table exists
        if ($wpdb->get_var("SHOW TABLES LIKE '$designs_table'") != $designs_table) {
            return 0; // Table doesn't exist
        }
        
        // First, try to find the design by exact URL match in the designs JSON
        $designs = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT options, designs FROM $designs_table WHERE designs LIKE %s",
                '%' . $wpdb->esc_like($filename) . '%'
            )
        );
        
        foreach ($designs as $design_record) {
            // Parse the designs JSON to find the specific design
            $designs_data = json_decode($design_record->designs, true);
            
            if (is_array($designs_data)) {
                foreach ($designs_data as $design) {
                    if (isset($design['source']) && strpos($design['source'], $filename) !== false) {
                        // Found the design, now check for price in its parameters
                        if (isset($design['parameters']['designs_parameter_price'])) {
                            $price = floatval($design['parameters']['designs_parameter_price']);
                            if ($price > 0) {
                                return $price;
                            }
                        }
                    }
                }
            }
            
            // Also check the category options for default price
            if (!empty($design_record->options)) {
                $options = json_decode($design_record->options, true);
                if (isset($options['designs_parameter_price'])) {
                    $price = floatval($options['designs_parameter_price']);
                    if ($price > 0) {
                        return $price;
                    }
                }
            }
        }
        
        // Fallback: Check if there's a global default price for designs
        if (function_exists('fpd_get_option')) {
            $default_price = fpd_get_option('fpd_designs_parameter_price', 0);
            if ($default_price > 0) {
                return floatval($default_price);
            }
        }
        
        // No price found, return 0 (free)
        return 0;
    }
    
    private function format_price($price) {
        if ($price <= 0) {
            return __('Free', 'radykal');
        }
        
        if (function_exists('wc_price')) {
            return strip_tags(wc_price($price));
        }
        
        $currency_symbol = function_exists('get_woocommerce_currency_symbol') ? get_woocommerce_currency_symbol() : '$';
        return $currency_symbol . number_format($price, 2);
    }
}

// Initialize the plugin
new FPD_Database_Price_Display();
