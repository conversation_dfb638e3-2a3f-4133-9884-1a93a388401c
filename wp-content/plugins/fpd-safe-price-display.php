<?php
/*
Plugin Name: FPD Safe Price Display
Description: Safe and simple price display for FPD design items - no function dependencies
Version: 1.0.0
Author: Custom Enhancement
*/

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class FPD_Safe_Price_Display {
    
    public function __construct() {
        add_action('wp_head', array($this, 'add_price_display_css'));
        add_action('wp_footer', array($this, 'add_price_display_js'));
    }
    
    public function add_price_display_css() {
        ?>
        <style type="text/css">
        /* FPD Design Item Price Display - Safe Version */
        .fpd-item {
            position: relative;
        }
        
        .fpd-item .fpd-item-price {
            position: absolute;
            top: 5px;
            right: 5px;
            background: rgba(0, 0, 0, 0.8);
            color: #fff;
            padding: 3px 6px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: bold;
            z-index: 10;
            line-height: 1;
            min-width: 20px;
            text-align: center;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
        }
        
        .fpd-item .fpd-item-price.fpd-free {
            background: rgba(46, 204, 113, 0.9);
        }
        
        .fpd-item .fpd-item-price.fpd-premium {
            background: rgba(231, 76, 60, 0.9);
        }
        
        /* CSS-only version using data attributes */
        .fpd-item[data-price]:not([data-price=""]):not([data-price="0"]):after {
            content: attr(data-price);
            position: absolute;
            top: 5px;
            right: 5px;
            background: rgba(231, 76, 60, 0.9);
            color: #fff;
            padding: 3px 6px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: bold;
            z-index: 10;
            line-height: 1;
            min-width: 20px;
            text-align: center;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
        }
        
        .fpd-item[data-price="0"]:after,
        .fpd-item[data-price="free"]:after,
        .fpd-item[data-price="Free"]:after {
            content: "Free";
            background: rgba(46, 204, 113, 0.9);
        }
        
        /* Alternative: Use data-price-display attribute for formatted prices */
        .fpd-item[data-price-display]:not([data-price-display=""]):after {
            content: attr(data-price-display);
        }
        
        /* Hide price on hover to see design better */
        .fpd-item:hover .fpd-item-price,
        .fpd-item:hover:after {
            opacity: 0.7;
            transition: opacity 0.2s ease;
        }
        </style>
        <?php
    }
    
    public function add_price_display_js() {
        ?>
        <script type="text/javascript">
        jQuery(document).ready(function($) {
            
            // Configuration
            var config = {
                designItemSelector: '.fpd-item[data-source]',
                priceClass: 'fpd-item-price',
                freeClass: 'fpd-free',
                premiumClass: 'fpd-premium',
                defaultPremiumPrice: 5.00,
                currencySymbol: '$'
            };
            
            // Try to get WooCommerce currency symbol if available
            if (typeof woocommerce_params !== 'undefined' && woocommerce_params.currency_symbol) {
                config.currencySymbol = woocommerce_params.currency_symbol;
            }
            
            // Function to add price to design item
            function addPriceToDesignItem($item) {
                
                // Skip if price already added
                if ($item.find('.' + config.priceClass).length > 0 || $item.attr('data-price-processed') === 'true') {
                    return;
                }
                
                var designUrl = $item.attr('data-source');
                var designTitle = $item.attr('data-title') || '';
                
                if (!designUrl) {
                    return;
                }
                
                var price = getDesignPrice($item, designUrl, designTitle);
                
                if (price !== null) {
                    displayPrice($item, price);
                    $item.attr('data-price-processed', 'true');
                }
            }
            
            // Function to get design price from various sources
            function getDesignPrice($item, designUrl, designTitle) {
                
                // Method 1: Check data attributes
                var price = $item.attr('data-price');
                if (price !== undefined && price !== '') {
                    return parseFloat(price);
                }
                
                // Method 2: Check if there's a price in the title or filename
                var filename = designUrl.split('/').pop().toLowerCase();
                
                // Look for price patterns in filename
                var pricePatterns = [
                    /price[_-]?(\d+(?:\.\d{2})?)/,
                    /\$(\d+(?:\.\d{2})?)/,
                    /(\d+(?:\.\d{2})?)[_-]?(?:usd|eur|gbp|dollar)/
                ];
                
                for (var i = 0; i < pricePatterns.length; i++) {
                    var match = filename.match(pricePatterns[i]);
                    if (match) {
                        return parseFloat(match[1]);
                    }
                }
                
                // Method 3: Check for free/premium indicators
                if (filename.indexOf('free') !== -1 || filename.indexOf('gratis') !== -1) {
                    return 0;
                }
                
                if (filename.indexOf('premium') !== -1 || filename.indexOf('paid') !== -1) {
                    return config.defaultPremiumPrice;
                }
                
                // Method 4: Check title for price info
                if (designTitle) {
                    var titleLower = designTitle.toLowerCase();
                    if (titleLower.indexOf('free') !== -1) {
                        return 0;
                    }
                    if (titleLower.indexOf('premium') !== -1) {
                        return config.defaultPremiumPrice;
                    }
                }
                
                // Default: assume free if no price found
                return 0;
            }
            
            // Function to display price on design item
            function displayPrice($item, price) {
                
                // Remove existing price element
                $item.find('.' + config.priceClass).remove();
                
                var priceText, priceClass;
                
                if (price <= 0) {
                    priceText = 'Free';
                    priceClass = config.priceClass + ' ' + config.freeClass;
                } else {
                    priceText = config.currencySymbol + price.toFixed(2);
                    priceClass = config.priceClass + ' ' + config.premiumClass;
                }
                
                // Create and append price element
                var $priceElement = $('<span>', {
                    'class': priceClass,
                    'text': priceText,
                    'title': 'Design Price: ' + priceText
                });
                
                $item.append($priceElement);
                
                // Also set data attribute for CSS-only fallback
                $item.attr('data-price-display', priceText);
                
                // Add fade-in animation
                $priceElement.hide().fadeIn(300);
            }
            
            // Function to process all design items
            function processDesignItems() {
                $(config.designItemSelector).each(function() {
                    addPriceToDesignItem($(this));
                });
            }
            
            // Initial processing
            setTimeout(processDesignItems, 500);
            
            // Set up observer for dynamically loaded content
            if (typeof MutationObserver !== 'undefined') {
                var observer = new MutationObserver(function(mutations) {
                    var shouldProcess = false;
                    
                    mutations.forEach(function(mutation) {
                        if (mutation.type === 'childList') {
                            for (var i = 0; i < mutation.addedNodes.length; i++) {
                                var node = mutation.addedNodes[i];
                                if (node.nodeType === 1) { // Element node
                                    var $node = $(node);
                                    if ($node.is(config.designItemSelector) || $node.find(config.designItemSelector).length > 0) {
                                        shouldProcess = true;
                                        break;
                                    }
                                }
                            }
                        }
                    });
                    
                    if (shouldProcess) {
                        setTimeout(processDesignItems, 100);
                    }
                });
                
                // Start observing
                var targetNode = document.querySelector('.fpd-container') || document.body;
                observer.observe(targetNode, {
                    childList: true,
                    subtree: true
                });
            }
            
            // Periodic check for new items (fallback)
            setInterval(processDesignItems, 3000);
            
            // Listen for common events that might indicate new content
            $(document).on('click', '.fpd-btn, .fpd-tab, .fpd-module', function() {
                setTimeout(processDesignItems, 500);
            });
            
            // Listen for scroll events (lazy loading)
            $(window).on('scroll', function() {
                setTimeout(processDesignItems, 200);
            });
        });
        </script>
        <?php
    }
}

// Initialize the plugin
new FPD_Safe_Price_Display();
