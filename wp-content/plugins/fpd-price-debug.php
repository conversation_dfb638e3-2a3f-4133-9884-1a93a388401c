<?php
/*
Plugin Name: FPD Price Debug
Description: Debug tool to check FPD design prices in database
Version: 1.0.0
Author: Debug Tool
*/

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Add admin menu for debugging
add_action('admin_menu', function() {
    add_submenu_page(
        'tools.php',
        'FPD Price Debug',
        'FPD Price Debug',
        'manage_options',
        'fpd-price-debug',
        'fpd_price_debug_page'
    );
});

function fpd_price_debug_page() {
    global $wpdb;
    
    echo '<div class="wrap">';
    echo '<h1>FPD Design Price Debug</h1>';
    
    // Check if designs table exists
    $designs_table = $wpdb->prefix . 'fpd_designs';
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$designs_table'") == $designs_table;
    
    if (!$table_exists) {
        echo '<div class="notice notice-error"><p>FPD Designs table does not exist!</p></div>';
        echo '</div>';
        return;
    }
    
    echo '<h2>Database Structure</h2>';
    echo '<p>Table: <code>' . $designs_table . '</code> ✅ Exists</p>';
    
    // Get table structure
    $columns = $wpdb->get_results("DESCRIBE $designs_table");
    echo '<h3>Table Columns:</h3>';
    echo '<ul>';
    foreach ($columns as $column) {
        echo '<li><strong>' . $column->Field . '</strong> (' . $column->Type . ')</li>';
    }
    echo '</ul>';
    
    // Get sample data
    $designs = $wpdb->get_results("SELECT ID, title, options, designs FROM $designs_table LIMIT 5");
    
    echo '<h2>Sample Design Records</h2>';
    
    if (empty($designs)) {
        echo '<p>No designs found in database.</p>';
    } else {
        foreach ($designs as $design) {
            echo '<div style="border: 1px solid #ccc; padding: 10px; margin: 10px 0;">';
            echo '<h3>Design ID: ' . $design->ID . ' - ' . esc_html($design->title) . '</h3>';
            
            // Parse options
            if (!empty($design->options)) {
                $options = json_decode($design->options, true);
                echo '<h4>Category Options:</h4>';
                if (isset($options['designs_parameter_price'])) {
                    echo '<p><strong>Default Price:</strong> $' . $options['designs_parameter_price'] . '</p>';
                } else {
                    echo '<p>No default price set</p>';
                }
                echo '<details><summary>All Options</summary><pre>' . print_r($options, true) . '</pre></details>';
            }
            
            // Parse designs
            if (!empty($design->designs)) {
                $designs_data = json_decode($design->designs, true);
                echo '<h4>Individual Designs:</h4>';
                
                if (is_array($designs_data)) {
                    foreach ($designs_data as $index => $individual_design) {
                        echo '<div style="margin-left: 20px; border-left: 2px solid #ddd; padding-left: 10px;">';
                        echo '<h5>Design #' . ($index + 1) . '</h5>';
                        
                        if (isset($individual_design['source'])) {
                            echo '<p><strong>Source:</strong> ' . esc_html($individual_design['source']) . '</p>';
                        }
                        
                        if (isset($individual_design['title'])) {
                            echo '<p><strong>Title:</strong> ' . esc_html($individual_design['title']) . '</p>';
                        }
                        
                        if (isset($individual_design['parameters']['designs_parameter_price'])) {
                            echo '<p><strong>Individual Price:</strong> $' . $individual_design['parameters']['designs_parameter_price'] . '</p>';
                        } else {
                            echo '<p>No individual price set</p>';
                        }
                        
                        echo '<details><summary>All Parameters</summary><pre>' . print_r($individual_design['parameters'] ?? array(), true) . '</pre></details>';
                        echo '</div>';
                    }
                } else {
                    echo '<p>Invalid designs data format</p>';
                }
            }
            
            echo '</div>';
        }
    }
    
    // Check global settings
    echo '<h2>Global FPD Settings</h2>';
    if (function_exists('fpd_get_option')) {
        $global_design_price = fpd_get_option('fpd_designs_parameter_price', 'Not set');
        echo '<p><strong>Global Design Price:</strong> ' . $global_design_price . '</p>';
    } else {
        echo '<p>FPD functions not available</p>';
    }
    
    echo '</div>';
}

// Add AJAX endpoint for quick price lookup
add_action('wp_ajax_fpd_debug_price_lookup', function() {
    global $wpdb;
    
    $design_url = sanitize_text_field($_POST['design_url'] ?? '');
    
    if (empty($design_url)) {
        wp_send_json_error('No design URL provided');
    }
    
    $filename = basename($design_url);
    $designs_table = $wpdb->prefix . 'fpd_designs';
    
    $designs = $wpdb->get_results(
        $wpdb->prepare(
            "SELECT ID, title, options, designs FROM $designs_table WHERE designs LIKE %s",
            '%' . $wpdb->esc_like($filename) . '%'
        )
    );
    
    $result = array(
        'design_url' => $design_url,
        'filename' => $filename,
        'found_records' => count($designs),
        'prices_found' => array()
    );
    
    foreach ($designs as $design) {
        $designs_data = json_decode($design->designs, true);
        
        if (is_array($designs_data)) {
            foreach ($designs_data as $individual_design) {
                if (isset($individual_design['source']) && strpos($individual_design['source'], $filename) !== false) {
                    if (isset($individual_design['parameters']['designs_parameter_price'])) {
                        $result['prices_found'][] = array(
                            'category_id' => $design->ID,
                            'category_title' => $design->title,
                            'price' => $individual_design['parameters']['designs_parameter_price'],
                            'source' => 'individual_design'
                        );
                    }
                }
            }
        }
        
        // Check category default price
        if (!empty($design->options)) {
            $options = json_decode($design->options, true);
            if (isset($options['designs_parameter_price'])) {
                $result['prices_found'][] = array(
                    'category_id' => $design->ID,
                    'category_title' => $design->title,
                    'price' => $options['designs_parameter_price'],
                    'source' => 'category_default'
                );
            }
        }
    }
    
    wp_send_json_success($result);
});

// Add debug info to admin footer
add_action('admin_footer', function() {
    if (isset($_GET['page']) && $_GET['page'] === 'fpd-price-debug') {
        ?>
        <script>
        function testPriceLookup() {
            var designUrl = prompt('Enter design URL to test:');
            if (designUrl) {
                jQuery.post(ajaxurl, {
                    action: 'fpd_debug_price_lookup',
                    design_url: designUrl
                }, function(response) {
                    console.log('Price lookup result:', response);
                    alert('Check console for detailed results');
                });
            }
        }
        </script>
        <p><button onclick="testPriceLookup()" class="button">Test Price Lookup</button></p>
        <?php
    }
});
