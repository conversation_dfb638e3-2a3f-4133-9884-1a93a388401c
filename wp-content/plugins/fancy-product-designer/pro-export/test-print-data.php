<?php

return $array = [
    "name" => "test",
    "output_format" => "pdf",
    "print_ready" => true,
    "used_fonts" => [
        [
            "name" => "Aller",
            "url" => "http://wp.dep/wp-content/uploads/fpd_fonts/Aller.ttf"
        ]
    ],
    "include_font_files" => false,
    "summary_json" => false,
    "svg_data" => null,
    "image_data" => [],
    "product_data" => [
        [
            "title" => "Test",
            "thumbnail" => "",
            "elements" => [
                [
                    "title" => "Test Print File",
                    "source" => "Test Print File",
                    "parameters" => [
                        "angle" => 0,
                        "autoCenter" => false,
                        "autoSelect" => false,
                        "boundingBox" => "",
                        "boundingBoxMode" => "clipping",
                        "charSpacing" => 0,
                        "chargeAfterEditing" => false,
                        "colorLinkGroup" => false,
                        "colorPrices" => [],
                        "colors" => "",
                        "copyable" => false,
                        "cornerSize" => 24,
                        "curvable" => false,
                        "curveRadius" => 80,
                        "curveReverse" => false,
                        "curved" => false,
                        "direction" => "ltr",
                        "draggable" => false,
                        "editable" => true,
                        "evented" => true,
                        "excludeFromExport" => false,
                        "fill" => "#000000",
                        "fixed" => false,
                        "flipX" => false,
                        "flipY" => false,
                        "fontFamily" => "Aller",
                        "fontSize" => 18,
                        "fontStyle" => "normal",
                        "fontWeight" => "normal",
                        "height" => 20.34,
                        "isEditable" => true,
                        "left" => 500,
                        "letterSpacing" => 0,
                        "lineHeight" => 1,
                        "lockUniScaling" => true,
                        "locked" => false,
                        "maxCurveRadius" => "400",
                        "maxFontSize" => 1000,
                        "maxLength" => 0,
                        "maxLines" => 0,
                        "minFontSize" => 1,
                        "neonText" => false,
                        "numberPlaceholder" => false,
                        "objectCaching" => false,
                        "opacity" => 1,
                        "originX" => "center",
                        "originY" => "center",
                        "padding" => 10,
                        "patterns" => "",
                        "price" => 0,
                        "removable" => false,
                        "replace" => "",
                        "replaceInAllViews" => false,
                        "resizable" => false,
                        "rotatable" => false,
                        "scaleX" => 1,
                        "scaleY" => 1,
                        "shadowBlur" => 0,
                        "shadowColor" => "",
                        "shadowOffsetX" => 0,
                        "shadowOffsetY" => 0,
                        "showInColorSelection" => false,
                        "sku" => "",
                        "stroke" => null,
                        "strokeColors" => [],
                        "strokeWidth" => 0,
                        "text" => "Test Print File",
                        "textAlign" => "left",
                        "textBox" => false,
                        "textDecoration" => "normal",
                        "textLinkGroup" => "",
                        "textPlaceholder" => false,
                        "textTransform" => "none",
                        "top" => 300,
                        "topped" => false,
                        "uniScalingUnlockable" => false,
                        "width" => 109.71,
                        "widthFontSize" => 0,
                        "z" => -1,
                        "zChangeable" => false
                    ],
                    "type" => "text",
                    "printingBoxCoords" => [
                        "left" => 50.145,
                        "top" => 138.33
                    ]
                ]
            ],
            "options" => [
                "stageWidth" => 1000,
                "stageHeight" => 600,
                "printingBox" => [
                    "width" => 210,
                    "height" => 297,
                    "top" => 151.5,
                    "left" => 395,
                    "visibility" => false
                ],
                "output" => [
                    "width" => 210,
                    "height" => 297
                ]
            ],
            "locked" => false,
            "productTitle" => "Test"
        ]
    ],
    "dpi" => 300,
    "include_images" => null
];


?>