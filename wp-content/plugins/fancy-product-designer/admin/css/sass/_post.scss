/**************************
**** POST
**************************/
#fpd-meta-box {
	.menu {
		min-height: auto;

		> .item {
			width: 49%;
			margin-right: 0 !important;
			margin-left: 0 !important;
			font-size: 13px;
			justify-content: center;
			padding: 0 0 5px !important;
		}
	}

	.inside label {
		margin-top: 10px;
		margin-bottom: 5px;
		display: block;
	}
}

.select2-selection__clear {
	padding-right: 10px;
}
#fpd-modal-ips {
	.radykal-option-type--text .ui.input {
		width: 100%;
	}

	.select2-selection {
		border-color: #e6e9ed;
		padding: 4px 0;
		height: auto;
	}

	.select2-selection__clear {
		display: none !important;
	}

	.select2-search__field {
		min-height: auto;
	}

	.select2-selection__arrow {
		top: 7px;
	}

	.select2:focus,
	#fpd-modal-ips .select2 *:focus {
		box-shadow: none !important;
		outline: none !important;
	}

	.unbordered td {
		border: none !important;
	}
}

.gform-settings__content {
	max-width: inherit;
}
