/**************************
****RESET *******
**************************/
#fpd-react-root,
#fpd-react-root *,
.ui.modals,
.ui.modals * {
  font-size: 13px;
  box-sizing: border-box;
}

.ui.modals .dropdown,
.ui.modals .dropdown .item,
.ui.modals .sub.header {
  font-size: 13px !important;
}

#fpd-react-root,
.ui.modals {
  font-family: Lato, "Helvetica Neue", Helvetica, sans-serif;
}

/**************************
**** FPD BACKEND *******
**************************/
body.fpd-backend {
  background: #f7f7f7;
}

body.fpd-backend .ui.top.left.popup {
  margin-bottom: 40px;
  font-size: 12px !important;
}

body.fpd-backend [data-tooltip]:after {
  font-size: 12px !important;
}

body.fpd-backend .fpd-backend-footer {
  position: absolute;
  bottom: 10px;
  margin: 0;
  left: 50%;
  transform: translateX(-50%);
}

body.fpd-backend #wpfooter {
  display: none !important;
}

body.fpd-backend #wpcontent {
  padding-left: 0;
}

body.fpd-backend #wpbody-content {
  padding-bottom: 0;
}

body.fpd-backend .wrap {
  margin-top: 40px;
  margin-left: 20px;
  margin-right: 20px;
}

#wpbody-content {
  position: relative;
}

/**************************
**** NOTIFICATION *******
**************************/
.ui.container > .notice h4 {
  margin-top: 0;
}

.fpd-dismiss-notification {
  position: relative;
}

.fpd-dismiss-notification > p {
  margin-right: 20px;
}

/**************************
**** FORM *******
**************************/
.input-uploader > .button:first-child {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.input-uploader input[type=text] {
  border-radius: 0 !important;
}

.wp-picker-container,
.wp-picker-container * {
  box-sizing: content-box !important;
}

/* SELECT" */
.select2-drop {
  z-index: 999999 !important;
}

.select2-search__field {
  webkit-box-shadow: none !important;
  box-shadow: none !important;
}

/* SELECT LIST */
.radykal-select-sortable-list {
  margin: 10px 0 5px 0;
}

.radykal-select-sortable-list > li {
  display: inline-block;
  list-style: none;
  background: #e9e9e9;
  font-size: 10px;
  line-height: 18px;
  padding: 2px 6px;
  margin: 0 5px 5px 0;
  border-radius: 2px;
  cursor: move;
}

.radykal-select-sortable-list > li > span {
  display: inline-block;
  padding: 0 3px;
  cursor: pointer;
  vertical-align: bottom;
  font-size: 20px;
}

.radykal-select-sortable-list > li.radykal-sortable-placeholder {
  border: 1px dashed #e9e9e9;
  background: #f6f6f6;
  width: 80px;
  height: 22px;
  padding: 0;
}

/**************************
**** MISC *******
**************************/
.fpd-clearfix:before,
.fpd-clearfix:after {
  content: " ";
  display: block;
  height: 0;
  overflow: hidden;
}

.fpd-clearfix:after {
  clear: both;
}

.fpd-clearfix {
  zoom: 1;
}

.fpd-clear {
  clear: both;
}

.fpd-hidden {
  display: none !important;
}

.fpd-left {
  float: left;
}

.fpd-right {
  float: right;
}

#fpd-admin-topbar + .wrap,
#fpd-admin-topbar + #fpd-react-root {
  margin-top: 50px;
}

#fpd-admin-topbar {
  position: absolute;
  left: 0;
  top: 0;
  background-color: #fff;
  min-height: 50px;
  width: 100%;
  display: flex;
  justify-content: space-between;
}
#fpd-admin-topbar > div {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 0 20px;
}
#fpd-admin-topbar > div > a,
#fpd-admin-topbar > div button {
  color: #000;
  text-decoration: none;
  background: none !important;
  border: none !important;
  cursor: pointer;
}
#fpd-admin-topbar > div > a.current,
#fpd-admin-topbar > div button.current {
  font-weight: 700;
}
#fpd-admin-topbar > div > a:hover,
#fpd-admin-topbar > div button:hover {
  text-decoration: underline;
}

/**************************
**** POST
**************************/
#fpd-meta-box .menu {
  min-height: auto;
}
#fpd-meta-box .menu > .item {
  width: 49%;
  margin-right: 0 !important;
  margin-left: 0 !important;
  font-size: 13px;
  justify-content: center;
  padding: 0 0 5px !important;
}
#fpd-meta-box .inside label {
  margin-top: 10px;
  margin-bottom: 5px;
  display: block;
}

.select2-selection__clear {
  padding-right: 10px;
}

#fpd-modal-ips .radykal-option-type--text .ui.input {
  width: 100%;
}
#fpd-modal-ips .select2-selection {
  border-color: #e6e9ed;
  padding: 4px 0;
  height: auto;
}
#fpd-modal-ips .select2-selection__clear {
  display: none !important;
}
#fpd-modal-ips .select2-search__field {
  min-height: auto;
}
#fpd-modal-ips .select2-selection__arrow {
  top: 7px;
}
#fpd-modal-ips .select2:focus,
#fpd-modal-ips #fpd-modal-ips .select2 *:focus {
  box-shadow: none !important;
  outline: none !important;
}
#fpd-modal-ips .unbordered td {
  border: none !important;
}

.gform-settings__content {
  max-width: inherit;
}

.fpd-status-table td:nth-child(2) {
  font-weight: bold;
}

.fpd-status-table td em {
  text-transform: capitalize;
  font-style: normal;
}

.fpd-status-table td [data-tooltip] {
  float: right;
  font-size: 16x;
}

#fpd-modal-shortcodes textarea {
  height: 40px;
}

.fpd-print-jobs-table {
  margin-bottom: 40px !important;
}

.fpd-print-jobs-table .pagination.menu {
  padding: 0;
}

#fpd-get-started {
  padding: 40px 0 0;
  max-width: 80%;
  margin: auto;
}
#fpd-get-started > h1 {
  text-align: center;
  margin: 20px 0 60px;
}
#fpd-get-started .cards .card {
  min-width: auto;
}
#fpd-get-started .cards .image {
  padding: 20px 10px;
}
#fpd-get-started .cards .content {
  padding: 1.5em;
  text-align: center;
}
#fpd-get-started .cards .content .header {
  font-size: 20px;
}
#fpd-get-started .cards .content .description {
  font-size: 16px;
  padding-bottom: 10px;
}
#fpd-get-started .cards .extra.content {
  border-top: none !important;
  padding-top: 0;
}
#fpd-get-started #fpd-genius-banner {
  background: #fff;
  padding: 30px;
  position: relative;
  margin: 100px auto 0;
}
#fpd-get-started #fpd-genius-banner .content p {
  font-size: 14px;
  margin-right: 200px;
}
#fpd-get-started #fpd-genius-banner .content .button {
  margin-top: 10px;
}
#fpd-get-started #fpd-genius-banner .header {
  font-size: 18px;
  margin-bottom: 20px;
}
#fpd-get-started #fpd-genius-banner .icon {
  position: absolute;
  background: transparent;
  right: 0;
  left: auto;
  top: 40%;
  transform: translateY(-50%);
  font-size: 80px;
}
#fpd-get-started #fpd-genius-banner .icon:after {
  display: none;
}

#fpd-admin-get-started-banner {
  margin: 80px auto 40px;
  position: relative;
}
#fpd-admin-get-started-banner > .segment {
  position: relative;
  padding: 40px;
}
#fpd-admin-get-started-banner .column p {
  font-size: 16px;
}
.toplevel_page_fancy_product_designer [data-uf-button=button-main],
.toplevel_page_fancy_product_designer [data-uf-content=checklist] {
  display: none !important;
}