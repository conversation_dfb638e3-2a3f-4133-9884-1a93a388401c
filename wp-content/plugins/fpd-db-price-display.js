jQuery(document).ready(function($) {
    
    // Configuration
    const config = {
        designItemSelector: '.fpd-item[data-source]',
        priceClass: 'fpd-db-price',
        freeClass: 'fpd-free',
        premiumClass: 'fpd-premium',
        loadingClass: 'fpd-loading',
        checkInterval: 2000,
        priceCache: new Map(),
        processingUrls: new Set()
    };
    
    // Initialize price display
    function initDatabasePriceDisplay() {
        
        // Function to add price to design item
        function addPriceToDesignItem($item) {
            
            // Skip if price already added or being processed
            if ($item.find('.' + config.priceClass).length > 0 || $item.attr('data-price-processed') === 'true') {
                return;
            }
            
            const designUrl = $item.attr('data-source');
            
            if (!designUrl) {
                return;
            }
            
            // Skip if already processing this URL
            if (config.processingUrls.has(designUrl)) {
                return;
            }
            
            // Check cache first
            if (config.priceCache.has(designUrl)) {
                displayPrice($item, config.priceCache.get(designUrl));
                $item.attr('data-price-processed', 'true');
                return;
            }
            
            // Mark as processing
            config.processingUrls.add(designUrl);
            $item.attr('data-price-processed', 'true');
            
            // Show loading indicator
            showLoadingPrice($item);
            
            // Fetch price from database
            fetchPriceFromDatabase($item, designUrl);
        }
        
        // Function to show loading price indicator
        function showLoadingPrice($item) {
            const $loadingElement = $('<span>', {
                class: config.priceClass + ' ' + config.loadingClass,
                text: fpd_db_price_display.loading_text || '...',
                title: 'Loading price...'
            });
            
            $item.append($loadingElement);
        }
        
        // Function to fetch price from database via AJAX
        function fetchPriceFromDatabase($item, designUrl) {
            
            $.ajax({
                url: fpd_db_price_display.ajax_url,
                type: 'POST',
                data: {
                    action: 'fpd_get_design_price_from_db',
                    design_url: designUrl,
                    nonce: fpd_db_price_display.nonce
                },
                timeout: 10000, // 10 second timeout
                success: function(response) {
                    config.processingUrls.delete(designUrl);
                    
                    if (response.success && response.data.price !== undefined) {
                        const price = parseFloat(response.data.price);
                        
                        // Cache the result
                        config.priceCache.set(designUrl, price);
                        
                        // Display the price
                        displayPrice($item, price);
                        
                        console.log('FPD Price loaded for:', designUrl, 'Price:', price);
                    } else {
                        console.warn('FPD Price fetch failed:', response);
                        displayPrice($item, 0); // Show as free if fetch fails
                    }
                },
                error: function(xhr, status, error) {
                    config.processingUrls.delete(designUrl);
                    console.error('FPD Price AJAX error:', error);
                    
                    // Show as free if there's an error
                    displayPrice($item, 0);
                }
            });
        }
        
        // Function to display price on design item
        function displayPrice($item, price) {
            
            // Remove existing price elements (including loading)
            $item.find('.' + config.priceClass).remove();
            
            let priceText, priceClass;
            
            if (price <= 0) {
                priceText = fpd_db_price_display.free_text || 'Free';
                priceClass = config.priceClass + ' ' + config.freeClass;
            } else {
                // Format price
                if (fpd_db_price_display.currency_symbol) {
                    priceText = fpd_db_price_display.currency_symbol + price.toFixed(2);
                } else {
                    priceText = '$' + price.toFixed(2);
                }
                priceClass = config.priceClass + ' ' + config.premiumClass;
            }
            
            // Create and append price element
            const $priceElement = $('<span>', {
                class: priceClass,
                text: priceText,
                title: 'Design Price: ' + priceText
            });
            
            $item.append($priceElement);
            
            // Add fade-in animation
            $priceElement.hide().fadeIn(400);
        }
        
        // Function to process all design items
        function processDesignItems() {
            const $items = $(config.designItemSelector);
            
            if ($items.length === 0) {
                return;
            }
            
            console.log('FPD Processing', $items.length, 'design items for pricing');
            
            // Process items in batches to avoid overwhelming the server
            const batchSize = 5;
            let currentBatch = 0;
            
            function processBatch() {
                const startIndex = currentBatch * batchSize;
                const endIndex = Math.min(startIndex + batchSize, $items.length);
                
                for (let i = startIndex; i < endIndex; i++) {
                    addPriceToDesignItem($($items[i]));
                }
                
                currentBatch++;
                
                // Process next batch after a short delay
                if (endIndex < $items.length) {
                    setTimeout(processBatch, 500);
                }
            }
            
            processBatch();
        }
        
        // Initial processing with delay to ensure DOM is ready
        setTimeout(processDesignItems, 1000);
        
        // Set up observer for dynamically loaded content
        if (typeof MutationObserver !== 'undefined') {
            const observer = new MutationObserver(function(mutations) {
                let shouldProcess = false;
                
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList') {
                        for (let i = 0; i < mutation.addedNodes.length; i++) {
                            const node = mutation.addedNodes[i];
                            if (node.nodeType === 1) { // Element node
                                const $node = $(node);
                                if ($node.is(config.designItemSelector) || $node.find(config.designItemSelector).length > 0) {
                                    shouldProcess = true;
                                    break;
                                }
                            }
                        }
                    }
                });
                
                if (shouldProcess) {
                    console.log('FPD New design items detected, processing...');
                    setTimeout(processDesignItems, 300);
                }
            });
            
            // Start observing
            const targetNode = document.querySelector('.fpd-container') || document.body;
            observer.observe(targetNode, {
                childList: true,
                subtree: true
            });
        }
        
        // Periodic check for new items (fallback)
        setInterval(function() {
            const unprocessedItems = $(config.designItemSelector).filter(function() {
                return $(this).attr('data-price-processed') !== 'true';
            });
            
            if (unprocessedItems.length > 0) {
                console.log('FPD Found', unprocessedItems.length, 'unprocessed items');
                processDesignItems();
            }
        }, config.checkInterval);
        
        // Listen for common FPD events
        $(document).on('click', '.fpd-btn, .fpd-tab, .fpd-module, [data-module]', function() {
            setTimeout(processDesignItems, 800);
        });
        
        // Listen for scroll events (for lazy loading)
        let scrollTimeout;
        $(window).on('scroll', function() {
            clearTimeout(scrollTimeout);
            scrollTimeout = setTimeout(processDesignItems, 500);
        });
        
        // Listen for FPD-specific events if available
        if (typeof fancyProductDesigner !== 'undefined') {
            try {
                fancyProductDesigner.addEventListener('ready', function() {
                    console.log('FPD Ready event detected');
                    setTimeout(processDesignItems, 1000);
                });
                
                fancyProductDesigner.addEventListener('moduleSet', function(evt) {
                    console.log('FPD Module set event detected:', evt.detail);
                    setTimeout(processDesignItems, 500);
                });
            } catch (e) {
                console.log('FPD Event listeners not available:', e);
            }
        }
        
        console.log('FPD Database Price Display initialized');
    }
    
    // Initialize when DOM is ready
    initDatabasePriceDisplay();
    
    // Also try to initialize when FPD becomes available
    let fpdCheckAttempts = 0;
    const maxFpdCheckAttempts = 20;
    
    const checkForFPD = setInterval(function() {
        fpdCheckAttempts++;
        
        if (typeof fancyProductDesigner !== 'undefined') {
            clearInterval(checkForFPD);
            console.log('FPD detected, reinitializing price display');
            setTimeout(initDatabasePriceDisplay, 500);
        } else if (fpdCheckAttempts >= maxFpdCheckAttempts) {
            clearInterval(checkForFPD);
            console.log('FPD not detected after', maxFpdCheckAttempts, 'attempts');
        }
    }, 500);
    
});
