jQuery(document).ready(function($) {
    
    // Configuration
    const config = {
        designItemSelector: '.fpd-item[data-source]',
        priceClass: 'fpd-item-price',
        freeClass: 'fpd-free',
        premiumClass: 'fpd-premium',
        checkInterval: 1000, // Check for new items every second
        priceCache: new Map()
    };
    
    // Initialize price display
    function initDesignPriceDisplay() {
        
        // Function to add price to design item
        function addPriceToDesignItem($item) {
            
            // Skip if price already added
            if ($item.find('.' + config.priceClass).length > 0) {
                return;
            }
            
            const designUrl = $item.attr('data-source');
            const designTitle = $item.attr('data-title') || '';
            
            if (!designUrl) {
                return;
            }
            
            // Check cache first
            if (config.priceCache.has(designUrl)) {
                displayPrice($item, config.priceCache.get(designUrl));
                return;
            }
            
            // Get price from various sources
            const price = getDesignPrice($item, designUrl, designTitle);
            
            if (price !== null) {
                config.priceCache.set(designUrl, price);
                displayPrice($item, price);
            } else {
                // Try to get price via AJAX if other methods fail
                fetchPriceViaAjax($item, designUrl, designTitle);
            }
        }
        
        // Function to get design price from various sources
        function getDesignPrice($item, designUrl, designTitle) {
            
            // Method 1: Check data attributes
            let price = $item.attr('data-price');
            if (price !== undefined) {
                return parseFloat(price);
            }
            
            // Method 2: Check if there's a price in the title or filename
            const filename = designUrl.split('/').pop().toLowerCase();
            
            // Look for price patterns in filename
            const pricePatterns = [
                /price[_-]?(\d+(?:\.\d{2})?)/,
                /\$(\d+(?:\.\d{2})?)/,
                /(\d+(?:\.\d{2})?)[_-]?(?:usd|eur|gbp)/
            ];
            
            for (let pattern of pricePatterns) {
                const match = filename.match(pattern);
                if (match) {
                    return parseFloat(match[1]);
                }
            }
            
            // Method 3: Check for premium/free indicators
            if (filename.includes('free') || filename.includes('gratis')) {
                return 0;
            }
            
            if (filename.includes('premium') || filename.includes('paid')) {
                return 5; // Default premium price
            }
            
            // Method 4: Check parent container for price info
            const $parent = $item.closest('[data-price]');
            if ($parent.length > 0) {
                price = $parent.attr('data-price');
                if (price !== undefined) {
                    return parseFloat(price);
                }
            }
            
            return null; // No price found
        }
        
        // Function to fetch price via AJAX
        function fetchPriceViaAjax($item, designUrl, designTitle) {
            
            if (typeof fpd_price_display === 'undefined') {
                return;
            }
            
            $.ajax({
                url: fpd_price_display.ajax_url,
                type: 'POST',
                data: {
                    action: 'fpd_get_design_price',
                    design_url: designUrl,
                    design_title: designTitle,
                    nonce: fpd_price_display.nonce
                },
                success: function(response) {
                    if (response.success && response.data.price !== undefined) {
                        const price = parseFloat(response.data.price);
                        config.priceCache.set(designUrl, price);
                        displayPrice($item, price);
                    }
                },
                error: function() {
                    // Fallback: show default price or free
                    displayPrice($item, 0);
                }
            });
        }
        
        // Function to display price on design item
        function displayPrice($item, price) {
            
            // Remove existing price element
            $item.find('.' + config.priceClass).remove();
            
            let priceText, priceClass;
            
            if (price <= 0) {
                priceText = fpd_price_display.free_text || 'Free';
                priceClass = config.priceClass + ' ' + config.freeClass;
            } else {
                // Format price
                if (typeof fpd_price_display !== 'undefined' && fpd_price_display.currency_symbol) {
                    priceText = fpd_price_display.currency_symbol + price.toFixed(2);
                } else {
                    priceText = '$' + price.toFixed(2);
                }
                priceClass = config.priceClass + ' ' + config.premiumClass;
            }
            
            // Create and append price element
            const $priceElement = $('<span>', {
                class: priceClass,
                text: priceText,
                title: 'Design Price: ' + priceText
            });
            
            $item.append($priceElement);
            
            // Add fade-in animation
            $priceElement.hide().fadeIn(300);
        }
        
        // Function to process all design items
        function processDesignItems() {
            $(config.designItemSelector).each(function() {
                addPriceToDesignItem($(this));
            });
        }
        
        // Initial processing
        processDesignItems();
        
        // Set up observer for dynamically loaded content
        const observer = new MutationObserver(function(mutations) {
            let shouldProcess = false;
            
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach(function(node) {
                        if (node.nodeType === 1) { // Element node
                            const $node = $(node);
                            if ($node.is(config.designItemSelector) || $node.find(config.designItemSelector).length > 0) {
                                shouldProcess = true;
                            }
                        }
                    });
                }
            });
            
            if (shouldProcess) {
                setTimeout(processDesignItems, 100);
            }
        });
        
        // Start observing
        const targetNode = document.querySelector('.fpd-container') || document.body;
        observer.observe(targetNode, {
            childList: true,
            subtree: true
        });
        
        // Periodic check for new items (fallback)
        setInterval(processDesignItems, config.checkInterval);
        
        // Listen for FPD events if available
        if (typeof fancyProductDesigner !== 'undefined') {
            fancyProductDesigner.addEventListener('ready', function() {
                setTimeout(processDesignItems, 500);
            });
            
            fancyProductDesigner.addEventListener('moduleSet', function() {
                setTimeout(processDesignItems, 300);
            });
        }
        
        // Listen for jQuery events
        $(document).on('fpd:designsLoaded fpd:moduleChanged', function() {
            setTimeout(processDesignItems, 200);
        });
    }
    
    // Initialize when DOM is ready
    initDesignPriceDisplay();
    
    // Also initialize when FPD is ready (if not already initialized)
    if (typeof fancyProductDesigner !== 'undefined') {
        fancyProductDesigner.addEventListener('ready', initDesignPriceDisplay);
    } else {
        // Wait for FPD to be available
        const checkFPD = setInterval(function() {
            if (typeof fancyProductDesigner !== 'undefined') {
                clearInterval(checkFPD);
                fancyProductDesigner.addEventListener('ready', initDesignPriceDisplay);
            }
        }, 100);
    }
    
});
