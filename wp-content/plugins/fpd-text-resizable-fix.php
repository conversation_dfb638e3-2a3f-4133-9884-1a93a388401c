<?php
/*
Plugin Name: FPD Text Resizable Fix
Description: Fix text resizable setting not working properly
Version: 1.0.0
Author: Custom Fix
*/

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class FPD_Text_Resizable_Fix {
    
    public function __construct() {
        // Hook into FPD frontend to override text parameters
        add_filter('fpd_element_parameters', array($this, 'fix_text_resizable'), 10, 3);
        
        // Also hook into the frontend localization
        add_filter('fpd_js_frontend_options', array($this, 'fix_frontend_text_options'), 10, 1);
        
        // Add admin page to check current settings
        add_action('admin_menu', array($this, 'add_debug_page'));
    }
    
    public function add_debug_page() {
        add_submenu_page(
            'tools.php',
            'FPD Text Settings Debug',
            'FPD Text Debug',
            'manage_options',
            'fpd-text-debug',
            array($this, 'debug_page')
        );
    }
    
    public function debug_page() {
        echo '<div class="wrap">';
        echo '<h1>FPD Text Resizable Settings Debug</h1>';
        
        // Check current setting values
        $resizable_setting = get_option('fpd_custom_texts_parameter_resizable', 'not_set');
        $draggable_setting = get_option('fpd_custom_texts_parameter_draggable', 'not_set');
        $rotatable_setting = get_option('fpd_custom_texts_parameter_rotatable', 'not_set');
        
        echo '<h2>Current Settings:</h2>';
        echo '<table class="widefat">';
        echo '<tr><th>Setting</th><th>Value</th><th>Status</th></tr>';
        echo '<tr><td>Text Resizable</td><td>' . $resizable_setting . '</td><td>' . ($resizable_setting === 'no' ? '✅ Disabled' : '❌ Enabled') . '</td></tr>';
        echo '<tr><td>Text Draggable</td><td>' . $draggable_setting . '</td><td>' . ($draggable_setting === 'no' ? '✅ Disabled' : '❌ Enabled') . '</td></tr>';
        echo '<tr><td>Text Rotatable</td><td>' . $rotatable_setting . '</td><td>' . ($rotatable_setting === 'no' ? '✅ Disabled' : '❌ Enabled') . '</td></tr>';
        echo '</table>';
        
        echo '<h2>Quick Fix:</h2>';
        echo '<p>If the resizable setting shows as "Enabled" but you want it disabled, click the button below:</p>';
        
        if (isset($_POST['force_disable_resizable'])) {
            update_option('fpd_custom_texts_parameter_resizable', 'no');
            echo '<div class="notice notice-success"><p>✅ Text resizable has been force-disabled!</p></div>';
            $resizable_setting = 'no';
        }
        
        echo '<form method="post">';
        echo '<input type="submit" name="force_disable_resizable" value="Force Disable Text Resizable" class="button button-primary" />';
        echo '</form>';
        
        echo '<h2>All FPD Text Settings:</h2>';
        echo '<details><summary>Click to view all FPD text-related options</summary>';
        
        // Get all FPD options
        global $wpdb;
        $fpd_options = $wpdb->get_results(
            "SELECT option_name, option_value FROM {$wpdb->options} WHERE option_name LIKE 'fpd_custom_texts_%' OR option_name LIKE 'fpd_font%'"
        );
        
        echo '<table class="widefat">';
        echo '<tr><th>Option Name</th><th>Value</th></tr>';
        foreach ($fpd_options as $option) {
            echo '<tr><td>' . esc_html($option->option_name) . '</td><td>' . esc_html($option->option_value) . '</td></tr>';
        }
        echo '</table>';
        echo '</details>';
        
        echo '</div>';
    }
    
    public function fix_text_resizable($parameters, $element_type, $element_data) {
        // Only apply to text elements
        if ($element_type === 'text' || (isset($element_data['type']) && $element_data['type'] === 'text')) {
            
            // Get the resizable setting
            $resizable_setting = get_option('fpd_custom_texts_parameter_resizable', 'yes');
            
            // Force override the resizable parameter
            if ($resizable_setting === 'no') {
                $parameters['resizable'] = false;
            }
            
            // Also check other settings
            $draggable_setting = get_option('fpd_custom_texts_parameter_draggable', 'yes');
            if ($draggable_setting === 'no') {
                $parameters['draggable'] = false;
            }
            
            $rotatable_setting = get_option('fpd_custom_texts_parameter_rotatable', 'yes');
            if ($rotatable_setting === 'no') {
                $parameters['rotatable'] = false;
            }
        }
        
        return $parameters;
    }
    
    public function fix_frontend_text_options($options) {
        // Override text parameters in frontend options
        $resizable_setting = get_option('fpd_custom_texts_parameter_resizable', 'yes');
        
        if ($resizable_setting === 'no') {
            // Ensure text parameters include resizable: false
            if (!isset($options['textParameters'])) {
                $options['textParameters'] = array();
            }
            $options['textParameters']['resizable'] = false;
        }
        
        return $options;
    }
}

// Initialize the fix
new FPD_Text_Resizable_Fix();

// Add a more aggressive JavaScript fix
add_action('wp_footer', function() {
    $resizable_setting = get_option('fpd_custom_texts_parameter_resizable', 'yes');

    if ($resizable_setting === 'no') {
        ?>
        <script type="text/javascript">
        jQuery(document).ready(function($) {
            console.log('FPD Text Fix: Starting DIRECT text resizable fix...');

            // Method 1: Override FPD options before initialization
            if (typeof window.fpd_frontend_options !== 'undefined') {
                if (!window.fpd_frontend_options.textParameters) {
                    window.fpd_frontend_options.textParameters = {};
                }
                window.fpd_frontend_options.textParameters.resizable = false;
                window.fpd_frontend_options.textParameters.lockScalingX = true;
                window.fpd_frontend_options.textParameters.lockScalingY = true;
                console.log('FPD Text Fix: Overrode frontend options');
            }

            // Method 1.5: Override global text parameters
            window.fpdTextFixApplied = false;

            // Method 2: Override when FPD is available
            function applyTextFix() {
                if (typeof fancyProductDesigner !== 'undefined') {
                    console.log('FPD Text Fix: FPD detected, applying fixes...');

                    // Override main options
                    if (fancyProductDesigner.mainOptions) {
                        if (!fancyProductDesigner.mainOptions.textParameters) {
                            fancyProductDesigner.mainOptions.textParameters = {};
                        }
                        fancyProductDesigner.mainOptions.textParameters.resizable = false;
                        console.log('FPD Text Fix: Main options updated');
                    }

                    // Override when text is added
                    fancyProductDesigner.addEventListener('elementAdd', function(evt) {
                        if (evt.detail && evt.detail.element) {
                            var element = evt.detail.element;
                            if (element.type === 'text' || element.type === 'i-text' || element.type === 'textbox' ||
                                (element.get && (element.get('type') === 'text' || element.get('type') === 'i-text' || element.get('type') === 'textbox'))) {
                                // Use multiple timeouts to ensure it works
                                setTimeout(function() {
                                    try {
                                        element.set({
                                            resizable: false,
                                            lockScalingX: true,
                                            lockScalingY: true
                                        });

                                        if (element.setControlsVisibility) {
                                            element.setControlsVisibility({
                                                mtr: false, // rotation
                                                ml: false,  // left middle
                                                mr: false,  // right middle
                                                mt: false,  // top middle
                                                mb: false,  // bottom middle
                                                tl: false,  // top left
                                                tr: false,  // top right
                                                bl: false,  // bottom left
                                                br: false   // bottom right
                                            });
                                        }

                                        console.log('FPD Text Fix: Text element made non-resizable via elementAdd');

                                        // Force canvas refresh
                                        if (fancyProductDesigner.currentViewInstance && fancyProductDesigner.currentViewInstance.stage) {
                                            fancyProductDesigner.currentViewInstance.stage.renderAll();
                                        }
                                    } catch (e) {
                                        console.log('FPD Text Fix: Error in elementAdd handler:', e);
                                    }
                                }, 50);

                                // Second attempt with longer delay
                                setTimeout(function() {
                                    try {
                                        element.set({
                                            resizable: false,
                                            lockScalingX: true,
                                            lockScalingY: true
                                        });
                                        if (element.setControlsVisibility) {
                                            element.setControlsVisibility({
                                                mtr: false, ml: false, mr: false, mt: false, mb: false,
                                                tl: false, tr: false, bl: false, br: false
                                            });
                                        }
                                    } catch (e) {
                                        console.log('FPD Text Fix: Error in delayed elementAdd handler:', e);
                                    }
                                }, 200);
                            }
                        }
                    });

                    // Override existing text elements
                    fancyProductDesigner.addEventListener('ready', function() {
                        setTimeout(function() {
                            if (fancyProductDesigner.currentViewInstance && fancyProductDesigner.currentViewInstance.stage) {
                                var objects = fancyProductDesigner.currentViewInstance.stage.getObjects();
                                objects.forEach(function(obj) {
                                    if (obj.type === 'text' || obj.get('type') === 'text') {
                                        obj.set({
                                            resizable: false,
                                            lockScalingX: true,
                                            lockScalingY: true
                                        });
                                        obj.setControlsVisibility({
                                            mtr: false,
                                            ml: false, mr: false, mt: false, mb: false,
                                            tl: false, tr: false, bl: false, br: false
                                        });
                                        console.log('FPD Text Fix: Existing text element fixed');
                                    }
                                });
                                fancyProductDesigner.currentViewInstance.stage.renderAll();
                            }
                        }, 500);
                    });

                    return true;
                }
                return false;
            }

            // Try to apply fix immediately
            if (!applyTextFix()) {
                // Wait for FPD to be available
                var attempts = 0;
                var checkFPD = setInterval(function() {
                    attempts++;
                    if (applyTextFix() || attempts > 50) {
                        clearInterval(checkFPD);
                        if (attempts > 50) {
                            console.log('FPD Text Fix: Timeout waiting for FPD');
                        }
                    }
                }, 200);
            }

            // Method 3: Monitor canvas for text objects
            function monitorCanvasForText() {
                if (fancyProductDesigner && fancyProductDesigner.currentViewInstance && fancyProductDesigner.currentViewInstance.stage) {
                    var canvas = fancyProductDesigner.currentViewInstance.stage;

                    // Override canvas add method
                    var originalAdd = canvas.add;
                    canvas.add = function(object) {
                        var result = originalAdd.apply(this, arguments);

                        if (object && (object.type === 'text' || object.type === 'i-text' || object.type === 'textbox' ||
                            (object.get && (object.get('type') === 'text' || object.get('type') === 'i-text' || object.get('type') === 'textbox')))) {
                            setTimeout(function() {
                                object.set({
                                    resizable: false,
                                    lockScalingX: true,
                                    lockScalingY: true
                                });
                                object.setControlsVisibility({
                                    mtr: false, ml: false, mr: false, mt: false, mb: false,
                                    tl: false, tr: false, bl: false, br: false
                                });
                                canvas.renderAll();
                                console.log('FPD Text Fix: Canvas add override - text made non-resizable');
                            }, 50);
                        }

                        return result;
                    };
                }
            }

            // Apply canvas monitoring when ready
            setTimeout(monitorCanvasForText, 2000);

            // Method 4: Direct object selection monitoring
            function setupSelectionMonitoring() {
                if (fancyProductDesigner && fancyProductDesigner.currentViewInstance && fancyProductDesigner.currentViewInstance.stage) {
                    var canvas = fancyProductDesigner.currentViewInstance.stage;

                    // Monitor selection events
                    canvas.on('selection:created', function(e) {
                        if (e.target && (e.target.type === 'text' || e.target.type === 'i-text' || e.target.type === 'textbox' ||
                            (e.target.get && (e.target.get('type') === 'text' || e.target.get('type') === 'i-text' || e.target.get('type') === 'textbox')))) {
                            fixTextElement(e.target);
                        }
                    });

                    canvas.on('selection:updated', function(e) {
                        if (e.target && (e.target.type === 'text' || e.target.type === 'i-text' || e.target.type === 'textbox' ||
                            (e.target.get && (e.target.get('type') === 'text' || e.target.get('type') === 'i-text' || e.target.get('type') === 'textbox')))) {
                            fixTextElement(e.target);
                        }
                    });

                    // Monitor object added events
                    canvas.on('object:added', function(e) {
                        if (e.target && (e.target.type === 'text' || e.target.type === 'i-text' || e.target.type === 'textbox' ||
                            (e.target.get && (e.target.get('type') === 'text' || e.target.get('type') === 'i-text' || e.target.get('type') === 'textbox')))) {
                            setTimeout(function() {
                                fixTextElement(e.target);
                            }, 100);
                        }
                    });

                    console.log('FPD Text Fix: Selection monitoring setup complete');
                }
            }

            function fixTextElement(textObj) {
                if (!textObj) return;

                try {
                    console.log('FPD Text Fix: Fixing text element properties...');

                    // Set properties directly
                    textObj.resizable = false;
                    textObj.lockScalingX = true;
                    textObj.lockScalingY = true;
                    textObj.hasControls = true; // Keep controls but hide resize ones

                    // Use set method as well
                    textObj.set({
                        resizable: false,
                        lockScalingX: true,
                        lockScalingY: true
                    });

                    // Hide resize controls
                    if (textObj.setControlsVisibility) {
                        textObj.setControlsVisibility({
                            mtr: true,  // Keep rotation
                            ml: false,  // Hide left middle
                            mr: false,  // Hide right middle
                            mt: false,  // Hide top middle
                            mb: false,  // Hide bottom middle
                            tl: false,  // Hide top left
                            tr: false,  // Hide top right
                            bl: false,  // Hide bottom left
                            br: false   // Hide bottom right
                        });
                    }

                    // Force refresh
                    if (fancyProductDesigner.currentViewInstance && fancyProductDesigner.currentViewInstance.stage) {
                        fancyProductDesigner.currentViewInstance.stage.renderAll();
                    }

                    console.log('FPD Text Fix: Text element fixed - resizable:', textObj.resizable);

                } catch (e) {
                    console.log('FPD Text Fix: Error fixing text element:', e);
                }
            }

            // Setup selection monitoring
            setTimeout(setupSelectionMonitoring, 3000);

            // Method 6: NUCLEAR OPTION - Override Fabric.js at the lowest level
            setTimeout(function() {
                if (typeof fabric !== 'undefined') {
                    console.log('FPD Text Fix: Applying NUCLEAR fabric.js override...');

                    // Override IText (interactive text) creation
                    if (fabric.IText) {
                        var originalIText = fabric.IText;
                        fabric.IText = function() {
                            var text = originalIText.apply(this, arguments);
                            if (text) {
                                text.resizable = false;
                                text.lockScalingX = true;
                                text.lockScalingY = true;
                                console.log('FPD Text Fix: IText created with resizable=false');
                            }
                            return text;
                        };
                        fabric.IText.prototype = originalIText.prototype;
                        fabric.IText.fromObject = originalIText.fromObject;
                    }

                    // Override Text creation
                    if (fabric.Text) {
                        var originalText = fabric.Text;
                        fabric.Text = function() {
                            var text = originalText.apply(this, arguments);
                            if (text) {
                                text.resizable = false;
                                text.lockScalingX = true;
                                text.lockScalingY = true;
                                console.log('FPD Text Fix: Text created with resizable=false');
                            }
                            return text;
                        };
                        fabric.Text.prototype = originalText.prototype;
                        fabric.Text.fromObject = originalText.fromObject;
                    }

                    // Override Textbox creation
                    if (fabric.Textbox) {
                        var originalTextbox = fabric.Textbox;
                        fabric.Textbox = function() {
                            var text = originalTextbox.apply(this, arguments);
                            if (text) {
                                text.resizable = false;
                                text.lockScalingX = true;
                                text.lockScalingY = true;
                                console.log('FPD Text Fix: Textbox created with resizable=false');
                            }
                            return text;
                        };
                        fabric.Textbox.prototype = originalTextbox.prototype;
                        fabric.Textbox.fromObject = originalTextbox.fromObject;
                    }
                }
            }, 1000);

            // Method 5: Continuous monitoring for ALL text types
            setInterval(function() {
                if (fancyProductDesigner && fancyProductDesigner.currentViewInstance && fancyProductDesigner.currentViewInstance.stage) {
                    var canvas = fancyProductDesigner.currentViewInstance.stage;
                    var activeObject = canvas.getActiveObject();

                    // Check for ALL text types: text, i-text, textbox
                    if (activeObject && (
                        activeObject.type === 'text' ||
                        activeObject.type === 'i-text' ||
                        activeObject.type === 'textbox' ||
                        (activeObject.get && (
                            activeObject.get('type') === 'text' ||
                            activeObject.get('type') === 'i-text' ||
                            activeObject.get('type') === 'textbox'
                        ))
                    )) {
                        if (activeObject.resizable === true) {
                            console.log('FPD Text Fix: Found resizable text (' + activeObject.type + '), fixing...');
                            fixTextElement(activeObject);
                        }
                    }
                }
            }, 500); // Check more frequently
        });
        </script>
        <?php
    }
});
