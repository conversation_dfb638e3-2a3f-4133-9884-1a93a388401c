<?php
/*
Plugin Name: FPD Text Resizable Fix
Description: Fix text resizable setting not working properly
Version: 1.0.0
Author: Custom Fix
*/

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class FPD_Text_Resizable_Fix {
    
    public function __construct() {
        // Hook into FPD frontend to override text parameters
        add_filter('fpd_element_parameters', array($this, 'fix_text_resizable'), 10, 3);
        
        // Also hook into the frontend localization
        add_filter('fpd_js_frontend_options', array($this, 'fix_frontend_text_options'), 10, 1);
        
        // Add admin page to check current settings
        add_action('admin_menu', array($this, 'add_debug_page'));
    }
    
    public function add_debug_page() {
        add_submenu_page(
            'tools.php',
            'FPD Text Settings Debug',
            'FPD Text Debug',
            'manage_options',
            'fpd-text-debug',
            array($this, 'debug_page')
        );
    }
    
    public function debug_page() {
        echo '<div class="wrap">';
        echo '<h1>FPD Text Resizable Settings Debug</h1>';
        
        // Check current setting values
        $resizable_setting = get_option('fpd_custom_texts_parameter_resizable', 'not_set');
        $draggable_setting = get_option('fpd_custom_texts_parameter_draggable', 'not_set');
        $rotatable_setting = get_option('fpd_custom_texts_parameter_rotatable', 'not_set');
        
        echo '<h2>Current Settings:</h2>';
        echo '<table class="widefat">';
        echo '<tr><th>Setting</th><th>Value</th><th>Status</th></tr>';
        echo '<tr><td>Text Resizable</td><td>' . $resizable_setting . '</td><td>' . ($resizable_setting === 'no' ? '✅ Disabled' : '❌ Enabled') . '</td></tr>';
        echo '<tr><td>Text Draggable</td><td>' . $draggable_setting . '</td><td>' . ($draggable_setting === 'no' ? '✅ Disabled' : '❌ Enabled') . '</td></tr>';
        echo '<tr><td>Text Rotatable</td><td>' . $rotatable_setting . '</td><td>' . ($rotatable_setting === 'no' ? '✅ Disabled' : '❌ Enabled') . '</td></tr>';
        echo '</table>';
        
        echo '<h2>Quick Fix:</h2>';
        echo '<p>If the resizable setting shows as "Enabled" but you want it disabled, click the button below:</p>';
        
        if (isset($_POST['force_disable_resizable'])) {
            update_option('fpd_custom_texts_parameter_resizable', 'no');
            echo '<div class="notice notice-success"><p>✅ Text resizable has been force-disabled!</p></div>';
            $resizable_setting = 'no';
        }
        
        echo '<form method="post">';
        echo '<input type="submit" name="force_disable_resizable" value="Force Disable Text Resizable" class="button button-primary" />';
        echo '</form>';
        
        echo '<h2>All FPD Text Settings:</h2>';
        echo '<details><summary>Click to view all FPD text-related options</summary>';
        
        // Get all FPD options
        global $wpdb;
        $fpd_options = $wpdb->get_results(
            "SELECT option_name, option_value FROM {$wpdb->options} WHERE option_name LIKE 'fpd_custom_texts_%' OR option_name LIKE 'fpd_font%'"
        );
        
        echo '<table class="widefat">';
        echo '<tr><th>Option Name</th><th>Value</th></tr>';
        foreach ($fpd_options as $option) {
            echo '<tr><td>' . esc_html($option->option_name) . '</td><td>' . esc_html($option->option_value) . '</td></tr>';
        }
        echo '</table>';
        echo '</details>';
        
        echo '</div>';
    }
    
    public function fix_text_resizable($parameters, $element_type, $element_data) {
        // Only apply to text elements
        if ($element_type === 'text' || (isset($element_data['type']) && $element_data['type'] === 'text')) {
            
            // Get the resizable setting
            $resizable_setting = get_option('fpd_custom_texts_parameter_resizable', 'yes');
            
            // Force override the resizable parameter
            if ($resizable_setting === 'no') {
                $parameters['resizable'] = false;
            }
            
            // Also check other settings
            $draggable_setting = get_option('fpd_custom_texts_parameter_draggable', 'yes');
            if ($draggable_setting === 'no') {
                $parameters['draggable'] = false;
            }
            
            $rotatable_setting = get_option('fpd_custom_texts_parameter_rotatable', 'yes');
            if ($rotatable_setting === 'no') {
                $parameters['rotatable'] = false;
            }
        }
        
        return $parameters;
    }
    
    public function fix_frontend_text_options($options) {
        // Override text parameters in frontend options
        $resizable_setting = get_option('fpd_custom_texts_parameter_resizable', 'yes');
        
        if ($resizable_setting === 'no') {
            // Ensure text parameters include resizable: false
            if (!isset($options['textParameters'])) {
                $options['textParameters'] = array();
            }
            $options['textParameters']['resizable'] = false;
        }
        
        return $options;
    }
}

// Initialize the fix
new FPD_Text_Resizable_Fix();

// Also add a direct JavaScript fix to ensure it works
add_action('wp_footer', function() {
    $resizable_setting = get_option('fpd_custom_texts_parameter_resizable', 'yes');
    
    if ($resizable_setting === 'no') {
        ?>
        <script type="text/javascript">
        jQuery(document).ready(function($) {
            // Override FPD text resizable setting
            if (typeof fancyProductDesigner !== 'undefined') {
                
                // Method 1: Override when FPD is ready
                fancyProductDesigner.addEventListener('ready', function() {
                    console.log('FPD Text Fix: Disabling text resizable...');
                    
                    // Override default text parameters
                    if (fancyProductDesigner.mainOptions && fancyProductDesigner.mainOptions.textParameters) {
                        fancyProductDesigner.mainOptions.textParameters.resizable = false;
                        console.log('FPD Text Fix: Text resizable disabled in main options');
                    }
                });
                
                // Method 2: Override when text is added
                fancyProductDesigner.addEventListener('elementAdd', function(evt) {
                    if (evt.detail && evt.detail.element && evt.detail.element.type === 'text') {
                        evt.detail.element.set('resizable', false);
                        console.log('FPD Text Fix: Text element made non-resizable');
                    }
                });
                
            } else {
                // Fallback: Wait for FPD to be available
                var checkFPD = setInterval(function() {
                    if (typeof fancyProductDesigner !== 'undefined') {
                        clearInterval(checkFPD);
                        
                        fancyProductDesigner.addEventListener('ready', function() {
                            console.log('FPD Text Fix: Disabling text resizable (fallback)...');
                            
                            if (fancyProductDesigner.mainOptions && fancyProductDesigner.mainOptions.textParameters) {
                                fancyProductDesigner.mainOptions.textParameters.resizable = false;
                            }
                        });
                    }
                }, 100);
            }
        });
        </script>
        <?php
    }
});
