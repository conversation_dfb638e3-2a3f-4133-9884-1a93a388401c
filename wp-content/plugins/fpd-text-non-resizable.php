<?php
/*
Plugin Name: FPD Text Non-Resizable
Description: Makes text elements non-resizable in Fancy Product Designer when the setting is disabled
Version: 1.0.0
Author: Custom Solution
*/

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class FPD_Text_Non_Resizable {
    
    public function __construct() {
        add_action('wp_footer', array($this, 'add_text_fix_script'));
    }
    
    public function add_text_fix_script() {
        // Only run on pages with FPD
        if (!$this->is_fpd_page()) {
            return;
        }
        
        $resizable_setting = get_option('fpd_custom_texts_parameter_resizable', 'yes');
        
        // Only add script if resizable is disabled
        if ($resizable_setting === 'no') {
            ?>
            <script type="text/javascript">
            jQuery(document).ready(function($) {
                console.log('FPD Text Non-Resizable: Initializing...');
                
                function fixTextElement(textObj) {
                    if (!textObj) return;
                    
                    try {
                        // Set properties directly
                        textObj.resizable = false;
                        textObj.lockScalingX = true;
                        textObj.lockScalingY = true;
                        
                        // Use set method as well
                        textObj.set({
                            resizable: false,
                            lockScalingX: true,
                            lockScalingY: true
                        });
                        
                        // Hide resize controls but keep rotation
                        if (textObj.setControlsVisibility) {
                            textObj.setControlsVisibility({
                                mtr: true,  // Keep rotation
                                ml: false,  // Hide left middle
                                mr: false,  // Hide right middle
                                mt: false,  // Hide top middle
                                mb: false,  // Hide bottom middle
                                tl: false,  // Hide top left
                                tr: false,  // Hide top right
                                bl: false,  // Hide bottom left
                                br: false   // Hide bottom right
                            });
                        }
                        
                        // Force canvas refresh
                        if (fancyProductDesigner && fancyProductDesigner.currentViewInstance && fancyProductDesigner.currentViewInstance.stage) {
                            fancyProductDesigner.currentViewInstance.stage.renderAll();
                        }
                        
                        console.log('FPD Text Non-Resizable: Fixed text element - resizable:', textObj.resizable);
                        
                    } catch (e) {
                        console.log('FPD Text Non-Resizable: Error fixing text element:', e);
                    }
                }
                
                function setupOptimizedTextFix() {
                    if (!fancyProductDesigner || !fancyProductDesigner.currentViewInstance) return false;
                    
                    var canvas = fancyProductDesigner.currentViewInstance.stage;
                    if (!canvas) return false;
                    
                    console.log('FPD Text Non-Resizable: Setting up optimized text fix...');
                    
                    // Hook into Fabric.js canvas events (most efficient)
                    canvas.on('object:added', function(e) {
                        var obj = e.target;
                        if (obj && (obj.type === 'i-text' || obj.type === 'text' || obj.type === 'textbox')) {
                            // Fix immediately when added
                            setTimeout(function() {
                                fixTextElement(obj);
                                console.log('FPD Text Non-Resizable: Fixed text on add:', obj.type);
                            }, 10);
                        }
                    });
                    
                    // Hook into selection events
                    canvas.on('selection:created', function(e) {
                        var obj = e.target;
                        if (obj && (obj.type === 'i-text' || obj.type === 'text' || obj.type === 'textbox')) {
                            fixTextElement(obj);
                            console.log('FPD Text Non-Resizable: Fixed text on selection:', obj.type);
                        }
                    });
                    
                    canvas.on('selection:updated', function(e) {
                        var obj = e.target;
                        if (obj && (obj.type === 'i-text' || obj.type === 'text' || obj.type === 'textbox')) {
                            fixTextElement(obj);
                            console.log('FPD Text Non-Resizable: Fixed text on selection update:', obj.type);
                        }
                    });
                    
                    // Hook into object modification events
                    canvas.on('object:modified', function(e) {
                        var obj = e.target;
                        if (obj && (obj.type === 'i-text' || obj.type === 'text' || obj.type === 'textbox')) {
                            // Re-apply fix after modification
                            setTimeout(function() {
                                fixTextElement(obj);
                            }, 10);
                        }
                    });
                    
                    return true;
                }
                
                // Try to setup optimized fix
                function initializeFix() {
                    if (typeof fancyProductDesigner !== 'undefined') {
                        if (!setupOptimizedTextFix()) {
                            // Wait a bit more for FPD to be fully ready
                            setTimeout(function() {
                                setupOptimizedTextFix();
                            }, 1000);
                        }
                        return true;
                    }
                    return false;
                }
                
                // Try immediate setup
                if (!initializeFix()) {
                    // Wait for FPD to be available
                    var attempts = 0;
                    var checkInterval = setInterval(function() {
                        attempts++;
                        if (initializeFix() || attempts > 50) {
                            clearInterval(checkInterval);
                            if (attempts > 50) {
                                console.log('FPD Text Non-Resizable: Could not initialize - FPD not found');
                            }
                        }
                    }, 200);
                }
            });
            </script>
            <?php
        }
    }
    
    private function is_fpd_page() {
        // Check if FPD is likely to be on this page
        global $post;
        
        // Basic checks for FPD presence
        if (function_exists('is_fancy_product') && $post && is_fancy_product($post->ID)) {
            return true;
        }
        
        // Check if FPD scripts are enqueued
        if (wp_script_is('fpd-frontend', 'enqueued') || wp_script_is('fpd-js', 'enqueued')) {
            return true;
        }
        
        // Fallback: check if we're on a product page
        if (function_exists('is_product') && is_product()) {
            return true;
        }
        
        return false;
    }
}

// Initialize the plugin
new FPD_Text_Non_Resizable();
