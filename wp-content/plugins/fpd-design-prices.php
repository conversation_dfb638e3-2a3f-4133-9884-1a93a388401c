<?php
/*
Plugin Name: FPD Design Prices
Description: Display design prices from FPD database in the design library
Version: 1.0.0
Author: Custom Solution
*/

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class FPD_Design_Prices {
    
    public function __construct() {
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('wp_ajax_fpd_get_design_price', array($this, 'ajax_get_design_price'));
        add_action('wp_ajax_nopriv_fpd_get_design_price', array($this, 'ajax_get_design_price'));
    }
    
    public function enqueue_scripts() {
        // Add CSS inline
        wp_add_inline_style('wp-block-library', $this->get_css());
        
        // Add JavaScript inline
        wp_enqueue_script('jquery');
        add_action('wp_footer', array($this, 'add_javascript'));
        
        // Localize script data
        wp_localize_script('jquery', 'fpd_prices', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('fpd_price_nonce'),
            'currency' => function_exists('get_woocommerce_currency_symbol') ? get_woocommerce_currency_symbol() : '$'
        ));
    }
    
    private function get_css() {
        return '
        .fpd-item {
            position: relative;
        }
        .fpd-price-badge {
            position: absolute;
            top: 5px;
            right: 5px;
            padding: 3px 6px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: bold;
            z-index: 10;
            line-height: 1;
            text-align: center;
            box-shadow: 0 1px 3px rgba(0,0,0,0.3);
            font-family: Arial, sans-serif;
        }
        .fpd-price-badge.free {
            background: rgba(46, 204, 113, 0.9);
            color: white;
        }
        .fpd-price-badge.paid {
            background: rgba(231, 76, 60, 0.9);
            color: white;
        }
        .fpd-price-badge.loading {
            background: rgba(52, 152, 219, 0.9);
            color: white;
            animation: pulse 1.5s infinite;
        }
        @keyframes pulse {
            0% { opacity: 0.6; }
            50% { opacity: 1; }
            100% { opacity: 0.6; }
        }
        .fpd-item:hover .fpd-price-badge {
            opacity: 0.7;
        }
        ';
    }
    
    public function add_javascript() {
        ?>
        <script type="text/javascript">
        jQuery(document).ready(function($) {
            console.log('FPD Design Prices: Starting...');
            
            var priceCache = {};
            var processing = {};
            
            function addPriceToDesign($item) {
                if ($item.find('.fpd-price-badge').length > 0) {
                    return; // Already has price
                }
                
                var designUrl = $item.attr('data-source');
                if (!designUrl) {
                    return;
                }
                
                var filename = designUrl.split('/').pop();
                console.log('FPD: Processing design:', filename);
                
                // Check cache
                if (priceCache[filename]) {
                    displayPrice($item, priceCache[filename]);
                    return;
                }
                
                // Avoid duplicate requests
                if (processing[filename]) {
                    return;
                }
                processing[filename] = true;
                
                // Show loading
                $item.append('<span class="fpd-price-badge loading">...</span>');
                
                // Get price via AJAX
                $.ajax({
                    url: fpd_prices.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'fpd_get_design_price',
                        design_url: designUrl,
                        nonce: fpd_prices.nonce
                    },
                    success: function(response) {
                        processing[filename] = false;
                        
                        if (response.success) {
                            var price = parseFloat(response.data.price);
                            priceCache[filename] = price;
                            displayPrice($item, price);
                            console.log('FPD: Price for', filename, '=', price);
                        } else {
                            console.log('FPD: No price found for', filename);
                            displayPrice($item, 0);
                        }
                    },
                    error: function() {
                        processing[filename] = false;
                        displayPrice($item, 0);
                    }
                });
            }
            
            function displayPrice($item, price) {
                $item.find('.fpd-price-badge').remove();
                
                var text, className;
                if (price > 0) {
                    text = fpd_prices.currency + price.toFixed(2);
                    className = 'fpd-price-badge paid';
                } else {
                    text = 'Free';
                    className = 'fpd-price-badge free';
                }
                
                $item.append('<span class="' + className + '">' + text + '</span>');
            }
            
            function processAllDesigns() {
                var $designs = $('.fpd-item[data-source]');
                console.log('FPD: Found', $designs.length, 'design items');
                
                $designs.each(function() {
                    addPriceToDesign($(this));
                });
            }
            
            // Initial processing
            setTimeout(processAllDesigns, 1000);
            
            // Watch for new designs
            var observer = new MutationObserver(function(mutations) {
                var hasNewDesigns = false;
                mutations.forEach(function(mutation) {
                    if (mutation.addedNodes.length > 0) {
                        for (var i = 0; i < mutation.addedNodes.length; i++) {
                            var node = mutation.addedNodes[i];
                            if (node.nodeType === 1) {
                                var $node = $(node);
                                if ($node.is('.fpd-item') || $node.find('.fpd-item').length > 0) {
                                    hasNewDesigns = true;
                                    break;
                                }
                            }
                        }
                    }
                });
                
                if (hasNewDesigns) {
                    setTimeout(processAllDesigns, 500);
                }
            });
            
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
            
            // Periodic check
            setInterval(processAllDesigns, 5000);
            
            // Listen for clicks that might load new designs
            $(document).on('click', '.fpd-btn, .fpd-tab, [data-module]', function() {
                setTimeout(processAllDesigns, 1000);
            });
        });
        </script>
        <?php
    }
    
    public function ajax_get_design_price() {
        check_ajax_referer('fpd_price_nonce', 'nonce');
        
        $design_url = sanitize_url($_POST['design_url']);
        $filename = basename($design_url);
        
        $price = $this->get_price_from_database($filename);
        
        wp_send_json_success(array(
            'price' => $price,
            'filename' => $filename
        ));
    }
    
    private function get_price_from_database($filename) {
        global $wpdb;
        
        $designs_table = $wpdb->prefix . 'fpd_designs';
        
        // Check if table exists
        if ($wpdb->get_var("SHOW TABLES LIKE '$designs_table'") != $designs_table) {
            return 0;
        }
        
        // Find designs containing this filename
        $designs = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT options, designs FROM $designs_table WHERE designs LIKE %s",
                '%' . $wpdb->esc_like($filename) . '%'
            )
        );
        
        foreach ($designs as $design_record) {
            // Check individual designs
            if (!empty($design_record->designs)) {
                $designs_data = json_decode($design_record->designs, true);
                if (is_array($designs_data)) {
                    foreach ($designs_data as $design) {
                        if (isset($design['source']) && strpos($design['source'], $filename) !== false) {
                            // Found matching design, check for price
                            if (isset($design['parameters']['designs_parameter_price'])) {
                                $price = floatval($design['parameters']['designs_parameter_price']);
                                if ($price > 0) {
                                    return $price;
                                }
                            }
                        }
                    }
                }
            }
            
            // Check category default price
            if (!empty($design_record->options)) {
                $options = json_decode($design_record->options, true);
                if (isset($options['designs_parameter_price'])) {
                    $price = floatval($options['designs_parameter_price']);
                    if ($price > 0) {
                        return $price;
                    }
                }
            }
        }
        
        // Check global default
        if (function_exists('fpd_get_option')) {
            $default_price = fpd_get_option('fpd_designs_parameter_price', 0);
            if ($default_price > 0) {
                return floatval($default_price);
            }
        }
        
        return 0; // Free
    }
}

// Initialize
new FPD_Design_Prices();
