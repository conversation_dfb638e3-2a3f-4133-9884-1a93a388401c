2025-06-14T19:11:43+00:00 CRITICAL Uncaught ArgumentCountError: Too few arguments to function is_fancy_product(), 0 passed in /Users/<USER>/Local Sites/coversbg2/app/public/wp-content/plugins/fpd-design-price-display.php on line 24 and exactly 1 expected in /Users/<USER>/Local Sites/coversbg2/app/public/wp-content/plugins/fancy-product-designer/inc/fpd-functions.php:4 CONTEXT: {"error":{"type":1,"file":"/Users/<USER>/Local Sites/coversbg2/app/public/wp-content/plugins/fancy-product-designer/inc/fpd-functions.php","line":4},"remote-logging":true,"backtrace":["","#0 /Users/<USER>/Local Sites/coversbg2/app/public/wp-content/plugins/fpd-design-price-display.php(24): is_fancy_product()","#1 /Users/<USER>/Local Sites/coversbg2/app/public/wp-includes/class-wp-hook.php(324): FPD_Design_Price_Display->enqueue_scripts('')","#2 /Users/<USER>/Local Sites/coversbg2/app/public/wp-includes/class-wp-hook.php(348): WP_Hook->apply_filters(NULL, Array)","#3 /Users/<USER>/Local Sites/coversbg2/app/public/wp-includes/plugin.php(517): WP_Hook->do_action(Array)","#4 /Users/<USER>/Local Sites/coversbg2/app/public/wp-includes/script-loader.php(2299): do_action('wp_enqueue_scri...')","#5 /Users/<USER>/Local Sites/coversbg2/app/public/wp-includes/class-wp-hook.php(324): wp_enqueue_scripts('')","#6 /Users/<USER>/Local Sites/coversbg2/app/public/wp-includes/class-wp-hook.php(348): WP_Hook->apply_filters(NULL, Array)","#7 /Users/<USER>/Local Sites/coversbg2/app/public/wp-includes/plugin.php(517): WP_Hook->do_action(Array)","#8 /Users/<USER>/Local Sites/coversbg2/app/public/wp-includes/general-template.php(3192): do_action('wp_head')","#9 /Users/<USER>/Local Sites/coversbg2/app/public/wp-content/themes/astra/header.php(31): wp_head()","#10 /Users/<USER>/Local Sites/coversbg2/app/public/wp-includes/template.php(810): require_once('/Users/<USER>')","#11 /Users/<USER>/Local Sites/coversbg2/app/public/wp-includes/template.php(745): load_template('/Users/<USER>', true, Array)","#12 /Users/<USER>/Local Sites/coversbg2/app/public/wp-includes/general-template.php(48): locate_template(Array, true, true, Array)","#13 /Users/<USER>/Local Sites/coversbg2/app/public/wp-content/plugins/woocommerce/templates/archive-product.php(20): get_header('shop')","#14 /Users/<USER>/Local Sites/coversbg2/app/public/wp-includes/template-loader.php(106): include('/Users/<USER>')","#15 /Users/<USER>/Local Sites/coversbg2/app/public/wp-blog-header.php(19): require_once('/Users/<USER>')","#16 /Users/<USER>/Local Sites/coversbg2/app/public/index.php(17): require('/Users/<USER>')","#17 {main}","thrown"]}
2025-06-14T19:11:53+00:00 CRITICAL Uncaught ArgumentCountError: Too few arguments to function is_fancy_product(), 0 passed in /Users/<USER>/Local Sites/coversbg2/app/public/wp-content/plugins/fpd-simple-price-display.php on line 169 and exactly 1 expected in /Users/<USER>/Local Sites/coversbg2/app/public/wp-content/plugins/fancy-product-designer/inc/fpd-functions.php:4 CONTEXT: {"error":{"type":1,"file":"/Users/<USER>/Local Sites/coversbg2/app/public/wp-content/plugins/fancy-product-designer/inc/fpd-functions.php","line":4},"remote-logging":true,"backtrace":["","#0 /Users/<USER>/Local Sites/coversbg2/app/public/wp-content/plugins/fpd-simple-price-display.php(169): is_fancy_product()","#1 /Users/<USER>/Local Sites/coversbg2/app/public/wp-content/plugins/fpd-simple-price-display.php(22): FPD_Simple_Price_Display->should_load()","#2 /Users/<USER>/Local Sites/coversbg2/app/public/wp-includes/class-wp-hook.php(324): FPD_Simple_Price_Display->add_price_display_css('')","#3 /Users/<USER>/Local Sites/coversbg2/app/public/wp-includes/class-wp-hook.php(348): WP_Hook->apply_filters(NULL, Array)","#4 /Users/<USER>/Local Sites/coversbg2/app/public/wp-includes/plugin.php(517): WP_Hook->do_action(Array)","#5 /Users/<USER>/Local Sites/coversbg2/app/public/wp-includes/general-template.php(3192): do_action('wp_head')","#6 /Users/<USER>/Local Sites/coversbg2/app/public/wp-content/themes/astra/header.php(31): wp_head()","#7 /Users/<USER>/Local Sites/coversbg2/app/public/wp-includes/template.php(810): require_once('/Users/<USER>')","#8 /Users/<USER>/Local Sites/coversbg2/app/public/wp-includes/template.php(745): load_template('/Users/<USER>', true, Array)","#9 /Users/<USER>/Local Sites/coversbg2/app/public/wp-includes/general-template.php(48): locate_template(Array, true, true, Array)","#10 /Users/<USER>/Local Sites/coversbg2/app/public/wp-content/plugins/woocommerce/templates/archive-product.php(20): get_header('shop')","#11 /Users/<USER>/Local Sites/coversbg2/app/public/wp-includes/template-loader.php(106): include('/Users/<USER>')","#12 /Users/<USER>/Local Sites/coversbg2/app/public/wp-blog-header.php(19): require_once('/Users/<USER>')","#13 /Users/<USER>/Local Sites/coversbg2/app/public/index.php(17): require('/Users/<USER>')","#14 {main}","thrown"]}
